<#
.SYNOPSIS
    Script para execução via Tactical RMM Script Manager - Gera inventário por cliente e envia por email

.DESCRIPTION
    Este script é projetado para ser executado pelo gerenciador de scripts do Tactical RMM.
    Recebe o ID do cliente como parâmetro, gera um inventário das estações organizadas
    por site e envia o relatório por email.

.PARAMETER ClientId
    ID do cliente no Tactical RMM (obrigatório)

.PARAMETER EmailTo
    Email de destino para envio do relatório (obrigatório)

.PARAMETER EmailSubject
    Assunto do email (opcional)

.PARAMETER IncludeOffline
    Incluir estações offline no relatório (padrão: true)

.EXAMPLE
    # Execução via Tactical RMM Script Manager
    # Argumentos: -ClientId 8 -EmailTo "<EMAIL>"
    
.NOTES
    Versão: 1.0
    Autor: NVirtual
    Data: 2025-01-03
    
    Este script é otimizado para execução via Tactical RMM Script Manager
    Os parâmetros devem ser passados como argumentos do script no Tactical
#>

param(
    [Parameter(Mandatory=$true)]
    [int]$ClientId,
    
    [Parameter(Mandatory=$true)]
    [string]$EmailTo,
    
    [string]$EmailSubject = "",
    [bool]$IncludeOffline = $true,
    [string]$SMTPServer = "smtp.gmail.com",
    [int]$SMTPPort = 587,
    [string]$SMTPUser = "",
    [string]$SMTPPassword = "",
    [switch]$VerboseOutput
)

# ============================================================================
# CONFIGURAÇÕES E INICIALIZAÇÃO
# ============================================================================

# Configurações da API Tactical RMM
$apiUrl = "https://api.centralmesh.nvirtual.com.br"
$apiToken = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"

# Configurações de email padrão
if (-not $SMTPUser) { $SMTPUser = "<EMAIL>" }
if (-not $SMTPPassword) { $SMTPPassword = "sua_senha_aqui" }

# ============================================================================
# FUNÇÕES AUXILIARES
# ============================================================================

function Write-TacticalLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "ERROR" { 
            Write-Host $logMessage -ForegroundColor Red
            Write-Error $Message
        }
        "WARN"  { Write-Host $logMessage -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
        default { Write-Host $logMessage }
    }
}

function Get-TacticalData {
    param([string]$Endpoint, [string]$Description)
    
    try {
        Write-TacticalLog "Buscando $Description..."
        $data = Invoke-RestMethod -Uri "$apiUrl/$Endpoint" -Headers @{
            "X-API-KEY" = $apiToken
        } -TimeoutSec 60
        
        Write-TacticalLog "Encontrados $($data.Count) registros de $Description" "SUCCESS"
        return $data
    } catch {
        Write-TacticalLog "Erro ao buscar $Description`: $($_.Exception.Message)" "ERROR"
        throw
    }
}

function Format-InventoryData {
    param($Agent, $Clients, $Sites)
    
    # Buscar informações do cliente e site
    $client = $Clients | Where-Object { $_.id -eq $Agent.client } | Select-Object -First 1
    $site = $Sites | Where-Object { $_.id -eq $Agent.site } | Select-Object -First 1
    
    # Determinar status de conectividade
    $status = if ($Agent.last_seen) {
        $lastSeen = [DateTime]::Parse($Agent.last_seen)
        $timeDiff = (Get-Date) - $lastSeen
        if ($timeDiff.TotalMinutes -lt 5) { "🟢 Online" }
        elseif ($timeDiff.TotalHours -lt 24) { "🟡 Recente" }
        else { "🔴 Offline" }
    } else { "❓ Desconhecido" }
    
    return [PSCustomObject]@{
        'Hostname' = $Agent.hostname
        'Status' = $status
        'Cliente' = if ($client) { $client.name } else { "N/A" }
        'Site' = if ($site) { $site.name } else { "N/A" }
        'Sistema_Operacional' = if ($Agent.operating_system) { $Agent.operating_system } else { "N/A" }
        'Arquitetura' = if ($Agent.arch) { $Agent.arch } else { "N/A" }
        'IP_Publico' = if ($Agent.public_ip) { $Agent.public_ip } else { "N/A" }
        'Versao_Agente' = if ($Agent.version) { $Agent.version } else { "N/A" }
        'Ultimo_Contato' = if ($Agent.last_seen) { 
            [DateTime]::Parse($Agent.last_seen).ToString("dd/MM/yyyy HH:mm:ss") 
        } else { "N/A" }
        'CPU_Modelo' = if ($Agent.cpu_model) { $Agent.cpu_model } else { "N/A" }
        'RAM_Total_GB' = if ($Agent.total_ram) { 
            [Math]::Round($Agent.total_ram / 1GB, 2) 
        } else { "N/A" }
        'Antivirus' = if ($Agent.antivirus) { $Agent.antivirus } else { "N/A" }
        'Usuario_Logado' = if ($Agent.logged_in_username) { $Agent.logged_in_username } else { "N/A" }
        'Servicos_Falhas' = if ($Agent.services_failing) { $Agent.services_failing } else { 0 }
        'Checks_Falhas' = if ($Agent.checks_failing) { $Agent.checks_failing } else { 0 }
        'Manutencao' = if ($Agent.maintenance_mode) { "Sim" } else { "Não" }
    }
}

function Generate-HTMLReport {
    param($InventoryData, $ClientName, $ReportStats)

    $html = @"
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Relatório de Inventário - $ClientName</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .header { background-color: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .header h1 { margin: 0; }
        .header p { margin: 5px 0; opacity: 0.9; }
        .stats { display: flex; gap: 15px; margin-bottom: 20px; flex-wrap: wrap; }
        .stat-card { background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); min-width: 150px; }
        .stat-number { font-size: 24px; font-weight: bold; color: #2c3e50; }
        .stat-label { color: #7f8c8d; font-size: 14px; }
        .site-section { background: white; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .site-header { background-color: #34495e; color: white; padding: 15px; border-radius: 8px 8px 0 0; }
        .site-header h2 { margin: 0; }
        .agents-table { width: 100%; border-collapse: collapse; }
        .agents-table th, .agents-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ecf0f1; }
        .agents-table th { background-color: #ecf0f1; font-weight: bold; }
        .agents-table tr:hover { background-color: #f8f9fa; }
        .status-online { color: #27ae60; font-weight: bold; }
        .status-recent { color: #f39c12; font-weight: bold; }
        .status-offline { color: #e74c3c; font-weight: bold; }
        .footer { text-align: center; margin-top: 30px; color: #7f8c8d; font-size: 12px; }
        .no-agents { padding: 20px; text-align: center; color: #7f8c8d; font-style: italic; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Relatório de Inventário Tactical RMM</h1>
        <p><strong>Cliente:</strong> $ClientName</p>
        <p><strong>Data/Hora:</strong> $(Get-Date -Format "dd/MM/yyyy HH:mm:ss")</p>
        <p><strong>Gerado por:</strong> NVirtual Tactical RMM</p>
    </div>

    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">$($ReportStats.TotalAgents)</div>
            <div class="stat-label">Total de Estações</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #27ae60;">$($ReportStats.OnlineCount)</div>
            <div class="stat-label">Online</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #f39c12;">$($ReportStats.RecentCount)</div>
            <div class="stat-label">Recente</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #e74c3c;">$($ReportStats.OfflineCount)</div>
            <div class="stat-label">Offline</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">$($ReportStats.SitesCount)</div>
            <div class="stat-label">Sites</div>
        </div>
    </div>
"@

    # Agrupar por site
    $siteGroups = $InventoryData | Group-Object Site | Sort-Object Name

    foreach ($siteGroup in $siteGroups) {
        $siteName = $siteGroup.Name
        $siteAgents = $siteGroup.Group | Sort-Object Hostname

        $html += @"
    <div class="site-section">
        <div class="site-header">
            <h2>🏢 $siteName ($($siteAgents.Count) estações)</h2>
        </div>
"@

        if ($siteAgents.Count -gt 0) {
            $html += @"
        <table class="agents-table">
            <thead>
                <tr>
                    <th>Hostname</th>
                    <th>Status</th>
                    <th>Sistema Operacional</th>
                    <th>CPU</th>
                    <th>RAM (GB)</th>
                    <th>Último Contato</th>
                    <th>Usuário</th>
                </tr>
            </thead>
            <tbody>
"@

            foreach ($agent in $siteAgents) {
                $statusClass = switch -Wildcard ($agent.Status) {
                    "*Online*" { "status-online" }
                    "*Recente*" { "status-recent" }
                    "*Offline*" { "status-offline" }
                    default { "" }
                }

                $html += @"
                <tr>
                    <td><strong>$($agent.Hostname)</strong></td>
                    <td class="$statusClass">$($agent.Status)</td>
                    <td>$($agent.Sistema_Operacional)</td>
                    <td>$($agent.CPU_Modelo)</td>
                    <td>$($agent.RAM_Total_GB)</td>
                    <td>$($agent.Ultimo_Contato)</td>
                    <td>$($agent.Usuario_Logado)</td>
                </tr>
"@
            }

            $html += @"
            </tbody>
        </table>
"@
        } else {
            $html += '<div class="no-agents">Nenhuma estação encontrada neste site</div>'
        }

        $html += '</div>'
    }

    $html += @"
    <div class="footer">
        <p>Relatório gerado automaticamente pelo sistema Tactical RMM da NVirtual</p>
        <p>Para mais informações, entre em contato com o suporte técnico</p>
    </div>
</body>
</html>
"@

    return $html
}

function Send-InventoryEmail {
    param($HtmlContent, $ClientName, $EmailTo, $EmailSubject, $ReportStats)

    try {
        # Definir assunto se não fornecido
        if (-not $EmailSubject) {
            $EmailSubject = "📊 Relatório de Inventário - $ClientName - $(Get-Date -Format 'dd/MM/yyyy')"
        }

        # Criar corpo do email em texto simples como fallback
        $textBody = @"
Relatório de Inventário Tactical RMM
Cliente: $ClientName
Data: $(Get-Date -Format "dd/MM/yyyy HH:mm:ss")

RESUMO:
- Total de Estações: $($ReportStats.TotalAgents)
- Online: $($ReportStats.OnlineCount)
- Recente: $($ReportStats.RecentCount)
- Offline: $($ReportStats.OfflineCount)
- Sites: $($ReportStats.SitesCount)

Este é um relatório automático gerado pelo sistema Tactical RMM da NVirtual.
Para visualizar o relatório completo, abra este email em um cliente que suporte HTML.

Atenciosamente,
Sistema Tactical RMM - NVirtual
"@

        Write-TacticalLog "Enviando email para: $EmailTo"

        # Configurar credenciais SMTP
        $securePassword = ConvertTo-SecureString $SMTPPassword -AsPlainText -Force
        $credential = New-Object System.Management.Automation.PSCredential($SMTPUser, $securePassword)

        # Enviar email
        Send-MailMessage -To $EmailTo -From $SMTPUser -Subject $EmailSubject -Body $HtmlContent -BodyAsHtml -SmtpServer $SMTPServer -Port $SMTPPort -UseSsl -Credential $credential

        Write-TacticalLog "Email enviado com sucesso!" "SUCCESS"
        return $true

    } catch {
        Write-TacticalLog "Erro ao enviar email: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# ============================================================================
# EXECUÇÃO PRINCIPAL
# ============================================================================

Write-TacticalLog "=== INICIANDO GERAÇÃO DE INVENTÁRIO PARA EMAIL ===" "SUCCESS"
Write-TacticalLog "Cliente ID: $ClientId"
Write-TacticalLog "Email destino: $EmailTo"

try {
    # Buscar dados da API
    $agents = Get-TacticalData -Endpoint "agents/" -Description "agentes"
    $clients = Get-TacticalData -Endpoint "clients/" -Description "clientes"
    $sites = Get-TacticalData -Endpoint "clients/sites/" -Description "sites"

    # Encontrar o cliente específico
    $targetClient = $clients | Where-Object { $_.id -eq $ClientId } | Select-Object -First 1
    if (-not $targetClient) {
        Write-TacticalLog "Cliente com ID $ClientId não encontrado!" "ERROR"
        exit 1
    }

    $clientName = $targetClient.name
    Write-TacticalLog "Cliente encontrado: $clientName" "SUCCESS"

    # Filtrar agentes do cliente
    $clientAgents = $agents | Where-Object { $_.client -eq $ClientId }
    Write-TacticalLog "Encontrados $($clientAgents.Count) agentes para o cliente $clientName"

    # Filtrar agentes offline se necessário
    if (-not $IncludeOffline) {
        $clientAgents = $clientAgents | Where-Object {
            if ($_.last_seen) {
                $lastSeen = [DateTime]::Parse($_.last_seen)
                $timeDiff = (Get-Date) - $lastSeen
                $timeDiff.TotalHours -lt 24
            } else {
                $false
            }
        }
        Write-TacticalLog "Filtrado agentes offline ($($clientAgents.Count) agentes restantes)"
    }

    if ($clientAgents.Count -eq 0) {
        Write-TacticalLog "Nenhum agente encontrado para o cliente especificado" "WARN"
        exit 0
    }

    # Processar dados dos agentes
    Write-TacticalLog "Processando dados de $($clientAgents.Count) agentes..."
    $inventoryData = @()

    foreach ($agent in $clientAgents) {
        $formattedAgent = Format-InventoryData -Agent $agent -Clients $clients -Sites $sites
        $inventoryData += $formattedAgent
    }

    # Calcular estatísticas
    $onlineCount = ($inventoryData | Where-Object { $_.Status -like "*Online*" }).Count
    $offlineCount = ($inventoryData | Where-Object { $_.Status -like "*Offline*" }).Count
    $recentCount = ($inventoryData | Where-Object { $_.Status -like "*Recente*" }).Count
    $sitesCount = ($inventoryData | Group-Object Site).Count

    $reportStats = @{
        TotalAgents = $inventoryData.Count
        OnlineCount = $onlineCount
        OfflineCount = $offlineCount
        RecentCount = $recentCount
        SitesCount = $sitesCount
    }

    Write-TacticalLog "=== ESTATÍSTICAS ===" "SUCCESS"
    Write-TacticalLog "Total: $($reportStats.TotalAgents) | Online: $onlineCount | Recente: $recentCount | Offline: $offlineCount | Sites: $sitesCount"

    # Gerar relatório HTML
    Write-TacticalLog "Gerando relatório HTML..."
    $htmlReport = Generate-HTMLReport -InventoryData $inventoryData -ClientName $clientName -ReportStats $reportStats

    # Enviar email
    $emailSent = Send-InventoryEmail -HtmlContent $htmlReport -ClientName $clientName -EmailTo $EmailTo -EmailSubject $EmailSubject -ReportStats $reportStats

    if ($emailSent) {
        Write-TacticalLog "=== RELATÓRIO ENVIADO COM SUCESSO ===" "SUCCESS"
        Write-TacticalLog "Cliente: $clientName"
        Write-TacticalLog "Destinatário: $EmailTo"
        Write-TacticalLog "Total de estações: $($reportStats.TotalAgents)"
    } else {
        Write-TacticalLog "Falha no envio do email" "ERROR"
        exit 1
    }

} catch {
    Write-TacticalLog "Erro durante a execução: $($_.Exception.Message)" "ERROR"
    exit 1
}

Write-TacticalLog "=== EXECUÇÃO FINALIZADA ===" "SUCCESS"
