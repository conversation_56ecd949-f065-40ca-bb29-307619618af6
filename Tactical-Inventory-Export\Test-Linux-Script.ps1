#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Script de teste para Export-TacticalInventory-Linux.ps1 no Ubuntu Server

.DESCRIPTION
    Este script testa diferentes cenários de execução do script de inventário
    no ambiente Ubuntu Server com PowerShell Core.

.PARAMETER TestMode
    Modo de teste a ser executado

.PARAMETER ClientId
    ID do cliente para teste (padrão: 8)

.PARAMETER EmailTo
    Email de destino para teste (padrão: <EMAIL>)

.EXAMPLE
    pwsh ./Test-Linux-Script.ps1 -TestMode environment
    pwsh ./Test-Linux-Script.ps1 -TestMode basic -ClientId 8 -EmailTo "<EMAIL>"

.NOTES
    Versão: 1.0
    Autor: NVirtual
    Data: 2025-01-03
    
    Requisitos:
    - Ubuntu Server com PowerShell Core
    - Módulo ImportExcel instalado
    - Conectividade com API Tactical RMM
#>

param(
    [ValidateSet('environment', 'modules', 'api', 'basic', 'excel', 'email', 'full', 'help')]
    [string]$TestMode = "environment",
    
    [int]$ClientId = 8,
    [string]$EmailTo = "<EMAIL>",
    [string]$OutputPath = "/tmp"
)

# Verificar se está rodando no Linux
if (-not $IsLinux) {
    Write-Error "❌ Este script deve ser executado no Linux com PowerShell Core"
    exit 1
}

# Função para log colorido
function Write-TestLog {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $prefix = "[$timestamp] [$Level]"
    
    switch ($Level) {
        "ERROR" { Write-Host "$prefix $Message" -ForegroundColor Red }
        "WARN"  { Write-Host "$prefix $Message" -ForegroundColor Yellow }
        "SUCCESS" { Write-Host "$prefix $Message" -ForegroundColor Green }
        "INFO" { Write-Host "$prefix $Message" -ForegroundColor Cyan }
        default { Write-Host "$prefix $Message" }
    }
}

function Test-Environment {
    Write-TestLog "=== TESTE DE AMBIENTE ===" "INFO"
    
    # Verificar sistema operacional
    $osInfo = uname -a
    Write-TestLog "Sistema: $osInfo" "INFO"
    
    # Verificar PowerShell
    Write-TestLog "PowerShell: $($PSVersionTable.PSVersion)" "SUCCESS"
    Write-TestLog "Edição: $($PSVersionTable.PSEdition)" "INFO"
    Write-TestLog "Plataforma: $($PSVersionTable.Platform)" "INFO"
    
    # Verificar variáveis de ambiente Linux
    Write-TestLog "IsLinux: $IsLinux" "INFO"
    Write-TestLog "IsWindows: $IsWindows" "INFO"
    Write-TestLog "IsMacOS: $IsMacOS" "INFO"
    
    # Verificar diretórios
    $testDirs = @("/tmp", "/opt", "/var/log")
    foreach ($dir in $testDirs) {
        if (Test-Path $dir) {
            Write-TestLog "Diretório $dir: ✅ Existe" "SUCCESS"
        } else {
            Write-TestLog "Diretório $dir: ❌ Não existe" "ERROR"
        }
    }
    
    # Verificar permissões de escrita
    $testFile = "/tmp/tactical-test-$(Get-Date -Format 'yyyyMMddHHmmss').txt"
    try {
        "teste" | Out-File -FilePath $testFile -Encoding UTF8
        if (Test-Path $testFile) {
            Remove-Item $testFile -Force
            Write-TestLog "Permissões de escrita em /tmp: ✅ OK" "SUCCESS"
        }
    } catch {
        Write-TestLog "Permissões de escrita em /tmp: ❌ Erro" "ERROR"
    }
}

function Test-Modules {
    Write-TestLog "=== TESTE DE MÓDULOS ===" "INFO"
    
    # Testar ImportExcel
    try {
        Import-Module ImportExcel -ErrorAction Stop
        $excelVersion = (Get-Module ImportExcel).Version
        Write-TestLog "Módulo ImportExcel: ✅ v$excelVersion" "SUCCESS"
        
        # Testar criação de Excel básico
        $testData = @(
            [PSCustomObject]@{ Nome = "Teste1"; Valor = 100 }
            [PSCustomObject]@{ Nome = "Teste2"; Valor = 200 }
        )
        
        $testExcelFile = "/tmp/test-excel-$(Get-Date -Format 'yyyyMMddHHmmss').xlsx"
        $testData | Export-Excel -Path $testExcelFile -AutoSize -WorksheetName "Teste"
        
        if (Test-Path $testExcelFile) {
            $fileSize = (Get-Item $testExcelFile).Length
            Write-TestLog "Arquivo Excel de teste criado: ✅ $fileSize bytes" "SUCCESS"
            Remove-Item $testExcelFile -Force
        } else {
            Write-TestLog "Falha ao criar arquivo Excel de teste" "ERROR"
        }
        
    } catch {
        Write-TestLog "Módulo ImportExcel: ❌ $($_.Exception.Message)" "ERROR"
        Write-TestLog "Execute: Install-Module ImportExcel -Force -Scope CurrentUser" "WARN"
    }
    
    # Testar outros módulos essenciais
    $essentialModules = @("Microsoft.PowerShell.Utility", "Microsoft.PowerShell.Management")
    foreach ($module in $essentialModules) {
        try {
            Import-Module $module -ErrorAction Stop
            Write-TestLog "Módulo $module: ✅ Carregado" "SUCCESS"
        } catch {
            Write-TestLog "Módulo $module: ❌ Erro" "ERROR"
        }
    }
}

function Test-API {
    Write-TestLog "=== TESTE DE API ===" "INFO"
    
    $apiUrl = "https://api.centralmesh.nvirtual.com.br"
    $apiToken = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"
    
    # Testar conectividade básica
    try {
        $headers = @{
            "X-API-KEY" = $apiToken
            "Content-Type" = "application/json"
        }
        
        Write-TestLog "Testando conectividade com API..." "INFO"
        $agents = Invoke-RestMethod -Uri "$apiUrl/agents/" -Headers $headers -TimeoutSec 30 -Method Get
        Write-TestLog "API Agents: ✅ $($agents.Count) agentes encontrados" "SUCCESS"
        
        $clients = Invoke-RestMethod -Uri "$apiUrl/clients/" -Headers $headers -TimeoutSec 30 -Method Get
        Write-TestLog "API Clients: ✅ $($clients.Count) clientes encontrados" "SUCCESS"
        
        $sites = Invoke-RestMethod -Uri "$apiUrl/clients/sites/" -Headers $headers -TimeoutSec 30 -Method Get
        Write-TestLog "API Sites: ✅ $($sites.Count) sites encontrados" "SUCCESS"
        
        # Testar cliente específico
        $targetClient = $clients | Where-Object { $_.id -eq $ClientId } | Select-Object -First 1
        if ($targetClient) {
            Write-TestLog "Cliente ID $ClientId encontrado: ✅ $($targetClient.name)" "SUCCESS"
        } else {
            Write-TestLog "Cliente ID $ClientId: ❌ Não encontrado" "ERROR"
        }
        
    } catch {
        Write-TestLog "Erro na API: ❌ $($_.Exception.Message)" "ERROR"
    }
}

function Test-BasicExecution {
    Write-TestLog "=== TESTE DE EXECUÇÃO BÁSICA ===" "INFO"
    
    $scriptPath = "./Export-TacticalInventory-Linux.ps1"
    
    if (-not (Test-Path $scriptPath)) {
        Write-TestLog "Script não encontrado: $scriptPath" "ERROR"
        return
    }
    
    Write-TestLog "Executando script com parâmetros de teste..." "INFO"
    Write-TestLog "ClientId: $ClientId" "INFO"
    Write-TestLog "EmailTo: $EmailTo" "INFO"
    Write-TestLog "OutputPath: $OutputPath" "INFO"
    
    try {
        # Executar script em modo de teste (sem envio de email real)
        $testEmailTo = "teste-nao-enviar@localhost"
        
        & $scriptPath -ClientId $ClientId -EmailTo $testEmailTo -OutputPath $OutputPath -SMTPPassword "teste123"
        
        Write-TestLog "Execução básica: ✅ Concluída" "SUCCESS"
        
    } catch {
        Write-TestLog "Erro na execução: ❌ $($_.Exception.Message)" "ERROR"
    }
}

function Test-ExcelGeneration {
    Write-TestLog "=== TESTE DE GERAÇÃO EXCEL ===" "INFO"
    
    # Dados de teste
    $testData = @(
        [PSCustomObject]@{
            'Hostname' = 'TEST-PC-01'
            'Status' = 'Online'
            'Cliente' = 'Cliente Teste'
            'Site' = 'Site Teste'
            'Sistema_Operacional' = 'Windows 10'
            'CPU_Modelo' = 'Intel i5'
            'RAM_Total_GB' = 8
        }
        [PSCustomObject]@{
            'Hostname' = 'TEST-PC-02'
            'Status' = 'Offline'
            'Cliente' = 'Cliente Teste'
            'Site' = 'Site Teste'
            'Sistema_Operacional' = 'Windows 11'
            'CPU_Modelo' = 'Intel i7'
            'RAM_Total_GB' = 16
        }
    )
    
    try {
        $testExcelFile = "/tmp/test-inventory-$(Get-Date -Format 'yyyyMMddHHmmss').xlsx"
        
        # Criar Excel com múltiplas abas
        $testData | Export-Excel -Path $testExcelFile -WorksheetName "Site Teste" -AutoSize -AutoFilter -FreezeTopRow -BoldTopRow
        
        # Adicionar aba de resumo
        $summaryData = @(
            [PSCustomObject]@{
                'Site' = 'Site Teste'
                'Total_Estacoes' = 2
                'Online' = 1
                'Offline' = 1
            }
        )
        $summaryData | Export-Excel -Path $testExcelFile -WorksheetName "Resumo" -AutoSize -AutoFilter -FreezeTopRow -BoldTopRow -Append
        
        if (Test-Path $testExcelFile) {
            $fileSize = (Get-Item $testExcelFile).Length
            Write-TestLog "Arquivo Excel de teste: ✅ $fileSize bytes" "SUCCESS"
            Write-TestLog "Localização: $testExcelFile" "INFO"
            
            # Manter arquivo para inspeção manual
            Write-TestLog "Arquivo mantido para inspeção manual" "INFO"
        } else {
            Write-TestLog "Falha ao criar arquivo Excel" "ERROR"
        }
        
    } catch {
        Write-TestLog "Erro na geração Excel: ❌ $($_.Exception.Message)" "ERROR"
    }
}

function Test-EmailConfiguration {
    Write-TestLog "=== TESTE DE CONFIGURAÇÃO EMAIL ===" "INFO"
    
    # Testar configurações SMTP básicas
    $smtpConfigs = @(
        @{ Server = "smtp.gmail.com"; Port = 587; Name = "Gmail" }
        @{ Server = "smtp.office365.com"; Port = 587; Name = "Office 365" }
    )
    
    foreach ($config in $smtpConfigs) {
        try {
            Write-TestLog "Testando conectividade SMTP: $($config.Name)" "INFO"
            
            # Testar conectividade TCP básica
            $tcpClient = New-Object System.Net.Sockets.TcpClient
            $tcpClient.ConnectAsync($config.Server, $config.Port).Wait(5000)
            
            if ($tcpClient.Connected) {
                Write-TestLog "$($config.Name): ✅ Conectividade OK" "SUCCESS"
                $tcpClient.Close()
            } else {
                Write-TestLog "$($config.Name): ❌ Sem conectividade" "WARN"
            }
            
        } catch {
            Write-TestLog "$($config.Name): ❌ Erro de conectividade" "WARN"
        }
    }
    
    Write-TestLog "Nota: Teste real de email requer credenciais válidas" "INFO"
}

function Test-FullWorkflow {
    Write-TestLog "=== TESTE COMPLETO ===" "INFO"
    
    Write-TestLog "Executando todos os testes..." "INFO"
    
    Test-Environment
    Test-Modules
    Test-API
    Test-ExcelGeneration
    Test-EmailConfiguration
    
    Write-TestLog "=== TESTE COMPLETO FINALIZADO ===" "SUCCESS"
}

function Show-Help {
    Write-Host "🆘 AJUDA - MODOS DE TESTE DISPONÍVEIS" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Modos disponíveis:" -ForegroundColor White
    Write-Host "  environment     - Testa ambiente Linux e PowerShell" -ForegroundColor Gray
    Write-Host "  modules         - Testa módulos PowerShell necessários" -ForegroundColor Gray
    Write-Host "  api             - Testa conectividade com API Tactical RMM" -ForegroundColor Gray
    Write-Host "  basic           - Execução básica do script principal" -ForegroundColor Gray
    Write-Host "  excel           - Testa geração de arquivos Excel" -ForegroundColor Gray
    Write-Host "  email           - Testa configurações de email" -ForegroundColor Gray
    Write-Host "  full            - Executa todos os testes" -ForegroundColor Gray
    Write-Host "  help            - Esta ajuda" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Exemplos de uso:" -ForegroundColor White
    Write-Host "  pwsh ./Test-Linux-Script.ps1 -TestMode environment" -ForegroundColor Gray
    Write-Host "  pwsh ./Test-Linux-Script.ps1 -TestMode api -ClientId 8" -ForegroundColor Gray
    Write-Host "  pwsh ./Test-Linux-Script.ps1 -TestMode full" -ForegroundColor Gray
}

# ============================================================================
# EXECUÇÃO PRINCIPAL
# ============================================================================

Write-TestLog "=== TESTE DO SCRIPT LINUX - TACTICAL INVENTORY ===" "SUCCESS"
Write-TestLog "Modo de teste: $TestMode" "INFO"
Write-TestLog "Sistema: $(uname -s) $(uname -r)" "INFO"
Write-TestLog "PowerShell: $($PSVersionTable.PSVersion)" "INFO"
Write-Host ""

switch ($TestMode.ToLower()) {
    "environment" { Test-Environment }
    "modules" { Test-Modules }
    "api" { Test-API }
    "basic" { Test-BasicExecution }
    "excel" { Test-ExcelGeneration }
    "email" { Test-EmailConfiguration }
    "full" { Test-FullWorkflow }
    "help" { Show-Help }
    default {
        Write-TestLog "Modo de teste '$TestMode' não reconhecido!" "ERROR"
        Show-Help
        exit 1
    }
}

Write-Host ""
Write-TestLog "=== TESTE FINALIZADO ===" "SUCCESS"
