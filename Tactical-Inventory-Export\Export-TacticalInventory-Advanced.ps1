<#
.SYNOPSIS
    Exporta inventário avançado de estações do Tactical RMM com informações detalhadas de hardware

.DESCRIPTION
    Este script conecta à API do Tactical RMM e exporta informações detalhadas
    das estações incluindo hardware, software, custom fields e histórico de checks
    para controle avançado de inventário.

.PARAMETER ApiToken
    Token de API do Tactical RMM

.PARAMETER ClientId
    ID do cliente específico para exportar

.PARAMETER OutputPath
    Caminho para salvar os arquivos

.PARAMETER IncludeHardware
    Inclui informações detalhadas de hardware

.PARAMETER IncludeSoftware
    Inclui lista de software instalado

.PARAMETER IncludeCustomFields
    Inclui custom fields configurados

.EXAMPLE
    .\Export-TacticalInventory-Advanced.ps1 -ClientId 8 -IncludeHardware -IncludeSoftware

.NOTES
    Versão: 1.0
    Autor: NVirtual
    Data: 2025-01-02
#>

param(
    [string]$ApiToken = $env:TACTICAL_RMM_TOKEN,
    [int]$ClientId,
    [string]$OutputPath = ".",
    [switch]$IncludeHardware,
    [switch]$IncludeSoftware,
    [switch]$IncludeCustomFields,
    [switch]$VerboseOutput
)

# Configurações da API
$apiUrl = "https://api.centralmesh.nvirtual.com.br"

if (-not $ApiToken) {
    $ApiToken = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"
}

# Verificar módulo ImportExcel
try {
    Import-Module ImportExcel -ErrorAction Stop
} catch {
    Write-Error "Módulo ImportExcel necessário. Execute: Install-Module ImportExcel -Scope CurrentUser"
    exit 1
}

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message"
}

function Get-AgentHardware {
    param([string]$AgentId, [string]$ApiUrl, [string]$Token)
    
    try {
        $hardware = Invoke-RestMethod -Uri "$ApiUrl/agents/$AgentId/wmi/" -Headers @{
            "X-API-KEY" = $Token
        } -TimeoutSec 30
        return $hardware
    } catch {
        Write-Log "Erro ao buscar hardware do agente $AgentId`: $($_.Exception.Message)" "WARN"
        return $null
    }
}

function Get-AgentSoftware {
    param([string]$AgentId, [string]$ApiUrl, [string]$Token)
    
    try {
        $software = Invoke-RestMethod -Uri "$ApiUrl/software/$AgentId/" -Headers @{
            "X-API-KEY" = $Token
        } -TimeoutSec 30
        return $software
    } catch {
        Write-Log "Erro ao buscar software do agente $AgentId`: $($_.Exception.Message)" "WARN"
        return $null
    }
}

function Get-AgentChecks {
    param([string]$AgentId, [string]$ApiUrl, [string]$Token)
    
    try {
        $checks = Invoke-RestMethod -Uri "$ApiUrl/checks/$AgentId/" -Headers @{
            "X-API-KEY" = $Token
        } -TimeoutSec 30
        return $checks
    } catch {
        Write-Log "Erro ao buscar checks do agente $AgentId`: $($_.Exception.Message)" "WARN"
        return $null
    }
}

function Format-HardwareData {
    param($Agent, $Hardware)
    
    if (-not $Hardware) { return $null }
    
    # Extrair informações principais de hardware
    $cpu = $Hardware | Where-Object { $_.wmi_class -eq "Win32_Processor" } | Select-Object -First 1
    $memory = $Hardware | Where-Object { $_.wmi_class -eq "Win32_PhysicalMemory" }
    $disk = $Hardware | Where-Object { $_.wmi_class -eq "Win32_LogicalDisk" -and $_.properties.DriveType -eq 3 }
    $motherboard = $Hardware | Where-Object { $_.wmi_class -eq "Win32_BaseBoard" } | Select-Object -First 1
    $bios = $Hardware | Where-Object { $_.wmi_class -eq "Win32_BIOS" } | Select-Object -First 1
    
    return [PSCustomObject]@{
        'Hostname' = $Agent.hostname
        'CPU_Nome' = if ($cpu) { $cpu.properties.Name } else { "N/A" }
        'CPU_Cores' = if ($cpu) { $cpu.properties.NumberOfCores } else { "N/A" }
        'CPU_Threads' = if ($cpu) { $cpu.properties.NumberOfLogicalProcessors } else { "N/A" }
        'CPU_Velocidade_MHz' = if ($cpu) { $cpu.properties.MaxClockSpeed } else { "N/A" }
        'RAM_Total_GB' = if ($memory) { 
            [Math]::Round(($memory | Measure-Object -Property @{Expression={$_.properties.Capacity}} -Sum).Sum / 1GB, 2)
        } else { "N/A" }
        'RAM_Slots_Usados' = if ($memory) { $memory.Count } else { "N/A" }
        'Disco_Principal_GB' = if ($disk) { 
            $mainDisk = $disk | Where-Object { $_.properties.DeviceID -eq "C:" } | Select-Object -First 1
            if ($mainDisk) { [Math]::Round($mainDisk.properties.Size / 1GB, 2) } else { "N/A" }
        } else { "N/A" }
        'Disco_Livre_GB' = if ($disk) { 
            $mainDisk = $disk | Where-Object { $_.properties.DeviceID -eq "C:" } | Select-Object -First 1
            if ($mainDisk) { [Math]::Round($mainDisk.properties.FreeSpace / 1GB, 2) } else { "N/A" }
        } else { "N/A" }
        'Placa_Mae' = if ($motherboard) { "$($motherboard.properties.Manufacturer) $($motherboard.properties.Product)" } else { "N/A" }
        'BIOS_Versao' = if ($bios) { $bios.properties.SMBIOSBIOSVersion } else { "N/A" }
        'Serial_Number' = if ($bios) { $bios.properties.SerialNumber } else { "N/A" }
    }
}

function Format-SoftwareData {
    param($Agent, $Software)
    
    if (-not $Software -or $Software.Count -eq 0) { return @() }
    
    $softwareList = @()
    foreach ($app in $Software) {
        $softwareList += [PSCustomObject]@{
            'Hostname' = $Agent.hostname
            'Nome_Software' = $app.name
            'Versao' = $app.version
            'Editor' = $app.publisher
            'Tamanho_MB' = if ($app.size) { [Math]::Round($app.size / 1MB, 2) } else { "N/A" }
            'Data_Instalacao' = if ($app.install_date) { $app.install_date } else { "N/A" }
        }
    }
    return $softwareList
}

# ============================================================================
# EXECUÇÃO PRINCIPAL
# ============================================================================

Write-Log "=== INICIANDO EXPORTAÇÃO AVANÇADA DE INVENTÁRIO ===" "SUCCESS"

try {
    # Buscar agentes
    Write-Log "Buscando agentes..."
    $agents = Invoke-RestMethod -Uri "$apiUrl/agents/" -Headers @{
        "X-API-KEY" = $ApiToken
    } -TimeoutSec 60
    
    # Filtrar por cliente se especificado
    if ($ClientId) {
        $agents = $agents | Where-Object { $_.client -eq $ClientId }
        Write-Log "Filtrado para cliente ID: $ClientId ($($agents.Count) agentes)"
    }
    
    if ($agents.Count -eq 0) {
        Write-Log "Nenhum agente encontrado" "WARN"
        exit 0
    }
    
    # Buscar clientes e sites para referência
    $clients = Invoke-RestMethod -Uri "$apiUrl/clients/" -Headers @{
        "X-API-KEY" = $ApiToken
    } -TimeoutSec 30
    
    $sites = Invoke-RestMethod -Uri "$apiUrl/clients/sites/" -Headers @{
        "X-API-KEY" = $ApiToken
    } -TimeoutSec 30
    
    # Preparar dados para exportação
    $inventoryData = @()
    $hardwareData = @()
    $softwareData = @()
    $customFieldsData = @()
    
    $counter = 0
    foreach ($agent in $agents) {
        $counter++
        Write-Progress -Activity "Processando agentes" -Status "Agente $counter de $($agents.Count): $($agent.hostname)" -PercentComplete (($counter / $agents.Count) * 100)
        
        # Dados básicos do inventário
        $client = $clients | Where-Object { $_.id -eq $agent.client } | Select-Object -First 1
        $site = $sites | Where-Object { $_.id -eq $agent.site } | Select-Object -First 1
        
        $basicData = [PSCustomObject]@{
            'ID_Agente' = $agent.agent_id
            'Hostname' = $agent.hostname
            'Cliente' = if ($client) { $client.name } else { "N/A" }
            'Site' = if ($site) { $site.name } else { "N/A" }
            'Sistema_Operacional' = $agent.operating_system
            'IP_Publico' = $agent.public_ip
            'Ultimo_Contato' = if ($agent.last_seen) { 
                [DateTime]::Parse($agent.last_seen).ToString("yyyy-MM-dd HH:mm:ss") 
            } else { "N/A" }
            'Status' = if ($agent.last_seen) {
                $lastSeen = [DateTime]::Parse($agent.last_seen)
                $timeDiff = (Get-Date) - $lastSeen
                if ($timeDiff.TotalMinutes -lt 5) { "Online" }
                elseif ($timeDiff.TotalHours -lt 24) { "Recente" }
                else { "Offline" }
            } else { "Desconhecido" }
        }
        $inventoryData += $basicData
        
        # Hardware detalhado
        if ($IncludeHardware) {
            Write-Log "Buscando hardware para $($agent.hostname)..."
            $hardware = Get-AgentHardware -AgentId $agent.agent_id -ApiUrl $apiUrl -Token $ApiToken
            $hwData = Format-HardwareData -Agent $agent -Hardware $hardware
            if ($hwData) { $hardwareData += $hwData }
        }
        
        # Software instalado
        if ($IncludeSoftware) {
            Write-Log "Buscando software para $($agent.hostname)..."
            $software = Get-AgentSoftware -AgentId $agent.agent_id -ApiUrl $apiUrl -Token $ApiToken
            $swData = Format-SoftwareData -Agent $agent -Software $software
            if ($swData.Count -gt 0) { $softwareData += $swData }
        }
        
        # Custom Fields
        if ($IncludeCustomFields -and $agent.custom_fields) {
            foreach ($field in $agent.custom_fields) {
                $customFieldsData += [PSCustomObject]@{
                    'Hostname' = $agent.hostname
                    'Campo_Nome' = $field.field
                    'Campo_Valor' = $field.value
                }
            }
        }
        
        # Pequena pausa para não sobrecarregar a API
        Start-Sleep -Milliseconds 100
    }
    
    Write-Progress -Activity "Processando agentes" -Completed
    
    # Gerar arquivo Excel com múltiplas abas
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $fileName = "TacticalRMM_Inventario_Avancado_$timestamp"
    if ($ClientId) { $fileName += "_Cliente$ClientId" }
    $filePath = Join-Path $OutputPath "$fileName.xlsx"
    
    Write-Log "Exportando para: $filePath"
    
    # Exportar dados básicos
    $inventoryData | Export-Excel -Path $filePath -WorksheetName "Inventario_Basico" -AutoSize -AutoFilter -FreezeTopRow -BoldTopRow
    
    # Exportar hardware se solicitado
    if ($IncludeHardware -and $hardwareData.Count -gt 0) {
        $hardwareData | Export-Excel -Path $filePath -WorksheetName "Hardware_Detalhado" -AutoSize -AutoFilter -FreezeTopRow -BoldTopRow
        Write-Log "Hardware exportado: $($hardwareData.Count) registros"
    }
    
    # Exportar software se solicitado
    if ($IncludeSoftware -and $softwareData.Count -gt 0) {
        $softwareData | Export-Excel -Path $filePath -WorksheetName "Software_Instalado" -AutoSize -AutoFilter -FreezeTopRow -BoldTopRow
        Write-Log "Software exportado: $($softwareData.Count) registros"
    }
    
    # Exportar custom fields se solicitado
    if ($IncludeCustomFields -and $customFieldsData.Count -gt 0) {
        $customFieldsData | Export-Excel -Path $filePath -WorksheetName "Custom_Fields" -AutoSize -AutoFilter -FreezeTopRow -BoldTopRow
        Write-Log "Custom Fields exportados: $($customFieldsData.Count) registros"
    }
    
    Write-Log "Exportação concluída com sucesso!" "SUCCESS"
    Write-Log "Arquivo salvo: $filePath" "SUCCESS"
    Write-Log "Total de agentes processados: $($inventoryData.Count)" "SUCCESS"

} catch {
    Write-Log "Erro durante a exportação: $($_.Exception.Message)" "ERROR"
    exit 1
}

Write-Log "=== EXPORTAÇÃO AVANÇADA FINALIZADA ===" "SUCCESS"
