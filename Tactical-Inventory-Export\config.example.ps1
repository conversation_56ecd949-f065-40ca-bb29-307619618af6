# ============================================================================
# ARQUIVO DE CONFIGURAÇÃO - TACTICAL INVENTORY EXPORT
# ============================================================================
# 
# Este arquivo contém as configurações padrão para os scripts de exportação
# de inventário do Tactical RMM.
# 
# INSTRUÇÕES:
# 1. Copie este arquivo para 'config.ps1'
# 2. Edite as configurações conforme necessário
# 3. Execute: . .\config.ps1 (para carregar as configurações)
#
# ============================================================================

# ============================================================================
# CONFIGURAÇÕES DA API
# ============================================================================

# URL da API do Tactical RMM
$Global:TACTICAL_API_URL = "https://api.centralmesh.nvirtual.com.br"

# Token de API - RECOMENDADO: Use variável de ambiente
# $env:TACTICAL_RMM_TOKEN = "SEU_TOKEN_AQUI"
$Global:TACTICAL_API_TOKEN = $env:TACTICAL_RMM_TOKEN

# Timeout para requisições da API (em segundos)
$Global:API_TIMEOUT = 60

# ============================================================================
# CONFIGURAÇÕES DE EXPORTAÇÃO
# ============================================================================

# Formato padrão de exportação ('Excel' ou 'CSV')
$Global:DEFAULT_OUTPUT_FORMAT = "Excel"

# Pasta padrão para salvar exportações
$Global:DEFAULT_OUTPUT_PATH = "C:\TacticalInventory\Exports"

# Incluir agentes offline por padrão
$Global:INCLUDE_OFFLINE_DEFAULT = $true

# Prefixo para nomes de arquivos
$Global:FILE_PREFIX = "TacticalRMM_Inventario"

# ============================================================================
# CONFIGURAÇÕES DE CLIENTES E SITES
# ============================================================================

# Mapeamento de nomes de clientes para IDs (para referência)
$Global:CLIENT_MAPPING = @{
    "NVirtual Info" = 1
    "Sumire" = 8
    # Adicione outros clientes conforme necessário
}

# Mapeamento de nomes de sites para IDs (para referência)
$Global:SITE_MAPPING = @{
    "Matriz" = 54
    "Bauru" = 1
    "São Paulo" = 4
    "CD - Sorocaba" = 40
    "Loja 01 - Avare" = 12
    "Loja 02 - Avare" = 13
    "Loja 03 - Marilia" = 14
    "Loja 04 - Aracatuba" = 15
    "Loja 05 - Presidente Prudente" = 16
    # Adicione outros sites conforme necessário
}

# ============================================================================
# CONFIGURAÇÕES DE HARDWARE
# ============================================================================

# Classes WMI para coleta de hardware (usado no script avançado)
$Global:WMI_CLASSES = @(
    "Win32_Processor",
    "Win32_PhysicalMemory", 
    "Win32_LogicalDisk",
    "Win32_BaseBoard",
    "Win32_BIOS",
    "Win32_ComputerSystem",
    "Win32_OperatingSystem"
)

# ============================================================================
# CONFIGURAÇÕES DE RELATÓRIOS
# ============================================================================

# Campos obrigatórios para exportação básica
$Global:REQUIRED_BASIC_FIELDS = @(
    "ID_Agente",
    "Hostname", 
    "Cliente",
    "Site",
    "Status",
    "Sistema_Operacional",
    "Ultimo_Contato"
)

# Campos opcionais para exportação básica
$Global:OPTIONAL_BASIC_FIELDS = @(
    "IP_Publico",
    "Agente_Versao",
    "CPU_Modelo",
    "RAM_Total_GB",
    "Antivirus",
    "Dominio",
    "Usuario_Logado"
)

# ============================================================================
# CONFIGURAÇÕES DE LOGGING
# ============================================================================

# Habilitar logging detalhado por padrão
$Global:VERBOSE_LOGGING = $false

# Pasta para logs
$Global:LOG_PATH = "C:\TacticalInventory\Logs"

# Manter logs por quantos dias
$Global:LOG_RETENTION_DAYS = 30

# ============================================================================
# CONFIGURAÇÕES DE PERFORMANCE
# ============================================================================

# Delay entre requisições para não sobrecarregar a API (em milissegundos)
$Global:API_DELAY_MS = 100

# Número máximo de agentes para processar em modo avançado por vez
$Global:MAX_AGENTS_BATCH = 50

# Timeout para operações de hardware (em segundos)
$Global:HARDWARE_TIMEOUT = 30

# ============================================================================
# CONFIGURAÇÕES DE SEGURANÇA
# ============================================================================

# Mascarar informações sensíveis nos logs
$Global:MASK_SENSITIVE_DATA = $true

# Campos considerados sensíveis (serão mascarados nos logs)
$Global:SENSITIVE_FIELDS = @(
    "Serial_Number",
    "Windows_Key",
    "IP_Publico"
)

# ============================================================================
# CONFIGURAÇÕES DE AUTOMAÇÃO
# ============================================================================

# Configurações para execução automatizada
$Global:AUTOMATION_CONFIG = @{
    # Executar exportação diária
    "DailyExport" = $false
    
    # Hora para execução diária (formato 24h)
    "DailyExportTime" = "06:00"
    
    # Executar exportação semanal avançada
    "WeeklyAdvancedExport" = $false
    
    # Dia da semana para exportação avançada (1=Segunda, 7=Domingo)
    "WeeklyExportDay" = 1
    
    # Enviar relatórios por email
    "EmailReports" = $false
    
    # Destinatários de email
    "EmailRecipients" = @("<EMAIL>")
}

# ============================================================================
# CONFIGURAÇÕES DE EMAIL (se habilitado)
# ============================================================================

$Global:EMAIL_CONFIG = @{
    "SMTPServer" = "smtp.gmail.com"
    "SMTPPort" = 587
    "UseSSL" = $true
    "From" = "<EMAIL>"
    "Subject" = "Relatório de Inventário Tactical RMM"
}

# ============================================================================
# FUNÇÕES AUXILIARES DE CONFIGURAÇÃO
# ============================================================================

function Initialize-TacticalInventoryConfig {
    <#
    .SYNOPSIS
        Inicializa as configurações e cria pastas necessárias
    #>
    
    # Criar pastas se não existirem
    $folders = @($Global:DEFAULT_OUTPUT_PATH, $Global:LOG_PATH)
    foreach ($folder in $folders) {
        if (-not (Test-Path $folder)) {
            New-Item -Path $folder -ItemType Directory -Force | Out-Null
            Write-Host "Pasta criada: $folder" -ForegroundColor Green
        }
    }
    
    # Verificar token de API
    if (-not $Global:TACTICAL_API_TOKEN) {
        Write-Warning "Token de API não configurado. Defina a variável de ambiente TACTICAL_RMM_TOKEN"
    }
    
    Write-Host "Configurações do Tactical Inventory Export carregadas com sucesso!" -ForegroundColor Green
}

function Show-TacticalInventoryConfig {
    <#
    .SYNOPSIS
        Exibe as configurações atuais
    #>
    
    Write-Host "=== CONFIGURAÇÕES TACTICAL INVENTORY EXPORT ===" -ForegroundColor Cyan
    Write-Host "API URL: $Global:TACTICAL_API_URL"
    Write-Host "Token configurado: $($Global:TACTICAL_API_TOKEN -ne $null)"
    Write-Host "Formato padrão: $Global:DEFAULT_OUTPUT_FORMAT"
    Write-Host "Pasta de saída: $Global:DEFAULT_OUTPUT_PATH"
    Write-Host "Incluir offline: $Global:INCLUDE_OFFLINE_DEFAULT"
    Write-Host "Logging verbose: $Global:VERBOSE_LOGGING"
    Write-Host "Clientes mapeados: $($Global:CLIENT_MAPPING.Count)"
    Write-Host "Sites mapeados: $($Global:SITE_MAPPING.Count)"
    Write-Host "================================================" -ForegroundColor Cyan
}

# ============================================================================
# INICIALIZAÇÃO AUTOMÁTICA
# ============================================================================

# Executar inicialização quando o arquivo for carregado
if ($MyInvocation.InvocationName -ne '.') {
    Initialize-TacticalInventoryConfig
}

# Exibir configurações se solicitado
if ($args -contains "-ShowConfig") {
    Show-TacticalInventoryConfig
}
