<#
.SYNOPSIS
    Exporta inventário de estações do Tactical RMM para controle de inventário

.DESCRIPTION
    Este script conecta à API do Tactical RMM e exporta informações detalhadas
    das estações/agentes para um arquivo Excel ou CSV, organizando por cliente
    e site para controle de inventário.

.PARAMETER ApiToken
    Token de API do Tactical RMM. Se não fornecido, usa a variável de ambiente
    TACTICAL_RMM_TOKEN.

.PARAMETER ClientId
    ID do cliente específico para exportar. Se não fornecido, exporta todos os clientes.

.PARAMETER SiteId
    ID do site específico para exportar. Se não fornecido, exporta todos os sites.

.PARAMETER OutputFormat
    Formato de saída: 'Excel' ou 'CSV'. Padrão: 'Excel'

.PARAMETER OutputPath
    Caminho para salvar o arquivo. Se não fornecido, salva na pasta atual.

.PARAMETER IncludeOffline
    Inclui agentes offline na exportação. Padrão: $true

.EXAMPLE
    .\Export-TacticalInventory.ps1
    Exporta inventário completo para Excel

.EXAMPLE
    .\Export-TacticalInventory.ps1 -ClientId 8 -OutputFormat CSV
    Exporta apenas o cliente ID 8 para CSV

.EXAMPLE
    .\Export-TacticalInventory.ps1 -SiteId 54 -OutputPath "C:\Inventario\"
    Exporta apenas o site ID 54 para pasta específica

.NOTES
    Versão: 1.0
    Autor: NVirtual
    Data: 2025-01-02
    
    Requisitos:
    - PowerShell 5.1 ou superior
    - Acesso à API do Tactical RMM
    - Token com permissões de leitura de agentes
    - Módulo ImportExcel (para formato Excel): Install-Module ImportExcel
#>

param(
    [string]$ApiToken = $env:TACTICAL_RMM_TOKEN,
    [int]$ClientId,
    [int]$SiteId,
    [ValidateSet('Excel', 'CSV')]
    [string]$OutputFormat = 'Excel',
    [string]$OutputPath = ".",
    [bool]$IncludeOffline = $true,
    [switch]$VerboseOutput
)

# ============================================================================
# CONFIGURAÇÕES E INICIALIZAÇÃO
# ============================================================================

# Configurações da API
$apiUrl = "https://api.centralmesh.nvirtual.com.br"

# Token padrão (recomenda-se usar variável de ambiente)
if (-not $ApiToken) {
    $ApiToken = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"
    Write-Warning "Usando token padrão. Recomenda-se definir TACTICAL_RMM_TOKEN como variável de ambiente."
}

# Verificar se o módulo ImportExcel está disponível para formato Excel
if ($OutputFormat -eq 'Excel') {
    try {
        Import-Module ImportExcel -ErrorAction Stop
    } catch {
        Write-Warning "Módulo ImportExcel não encontrado. Mudando para formato CSV."
        Write-Host "Para instalar: Install-Module ImportExcel -Scope CurrentUser"
        $OutputFormat = 'CSV'
    }
}

# ============================================================================
# FUNÇÕES AUXILIARES
# ============================================================================

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "WARN"  { Write-Host $logMessage -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
        default { Write-Host $logMessage }
    }
    
    if ($VerboseOutput) {
        Write-Verbose $logMessage
    }
}

function Get-TacticalAgents {
    param([string]$ApiUrl, [string]$Token)
    
    try {
        Write-Log "Buscando agentes na API do Tactical RMM..."
        $agents = Invoke-RestMethod -Uri "$ApiUrl/agents/" -Headers @{
            "X-API-KEY" = $Token
        } -TimeoutSec 60
        
        Write-Log "Encontrados $($agents.Count) agentes" "SUCCESS"
        return $agents
    } catch {
        Write-Log "Erro ao buscar agentes: $($_.Exception.Message)" "ERROR"
        throw
    }
}

function Get-TacticalClients {
    param([string]$ApiUrl, [string]$Token)
    
    try {
        Write-Log "Buscando clientes na API..."
        $clients = Invoke-RestMethod -Uri "$ApiUrl/clients/" -Headers @{
            "X-API-KEY" = $Token
        } -TimeoutSec 30
        
        Write-Log "Encontrados $($clients.Count) clientes" "SUCCESS"
        return $clients
    } catch {
        Write-Log "Erro ao buscar clientes: $($_.Exception.Message)" "ERROR"
        return @()
    }
}

function Get-TacticalSites {
    param([string]$ApiUrl, [string]$Token)
    
    try {
        Write-Log "Buscando sites na API..."
        $sites = Invoke-RestMethod -Uri "$ApiUrl/clients/sites/" -Headers @{
            "X-API-KEY" = $Token
        } -TimeoutSec 30
        
        Write-Log "Encontrados $($sites.Count) sites" "SUCCESS"
        return $sites
    } catch {
        Write-Log "Erro ao buscar sites: $($_.Exception.Message)" "ERROR"
        return @()
    }
}

function Format-AgentData {
    param($Agent, $Clients, $Sites)
    
    # Buscar informações do cliente e site
    $client = $Clients | Where-Object { $_.id -eq $Agent.client } | Select-Object -First 1
    $site = $Sites | Where-Object { $_.id -eq $Agent.site } | Select-Object -First 1
    
    # Determinar status de conectividade
    $status = if ($Agent.last_seen) {
        $lastSeen = [DateTime]::Parse($Agent.last_seen)
        $timeDiff = (Get-Date) - $lastSeen
        if ($timeDiff.TotalMinutes -lt 5) { "Online" }
        elseif ($timeDiff.TotalHours -lt 24) { "Recente" }
        else { "Offline" }
    } else { "Desconhecido" }
    
    # Extrair informações do sistema operacional
    $osInfo = if ($Agent.operating_system) { $Agent.operating_system } else { "N/A" }
    $osVersion = if ($Agent.plat) { $Agent.plat } else { "N/A" }
    
    return [PSCustomObject]@{
        'ID_Agente' = $Agent.agent_id
        'Hostname' = $Agent.hostname
        'Cliente' = if ($client) { $client.name } else { "N/A" }
        'Site' = if ($site) { $site.name } else { "N/A" }
        'Status' = $status
        'Sistema_Operacional' = $osInfo
        'Versao_OS' = $osVersion
        'Arquitetura' = if ($Agent.arch) { $Agent.arch } else { "N/A" }
        'IP_Publico' = if ($Agent.public_ip) { $Agent.public_ip } else { "N/A" }
        'Agente_Versao' = if ($Agent.version) { $Agent.version } else { "N/A" }
        'Ultimo_Contato' = if ($Agent.last_seen) { 
            [DateTime]::Parse($Agent.last_seen).ToString("yyyy-MM-dd HH:mm:ss") 
        } else { "N/A" }
        'Tempo_Boot' = if ($Agent.boot_time) { 
            [DateTime]::Parse($Agent.boot_time).ToString("yyyy-MM-dd HH:mm:ss") 
        } else { "N/A" }
        'CPU_Modelo' = if ($Agent.cpu_model) { $Agent.cpu_model } else { "N/A" }
        'RAM_Total_GB' = if ($Agent.total_ram) { 
            [Math]::Round($Agent.total_ram / 1GB, 2) 
        } else { "N/A" }
        'Espaco_Disco_GB' = if ($Agent.used_ram) { 
            [Math]::Round($Agent.used_ram / 1GB, 2) 
        } else { "N/A" }
        'Antivirus' = if ($Agent.antivirus) { $Agent.antivirus } else { "N/A" }
        'Dominio' = if ($Agent.domain) { $Agent.domain } else { "N/A" }
        'Usuario_Logado' = if ($Agent.logged_in_username) { $Agent.logged_in_username } else { "N/A" }
        'Servicos_Falhas' = if ($Agent.services_failing) { $Agent.services_failing } else { 0 }
        'Checks_Falhas' = if ($Agent.checks_failing) { $Agent.checks_failing } else { 0 }
        'Manutencao' = if ($Agent.maintenance_mode) { "Sim" } else { "Não" }
        'Monitoramento' = if ($Agent.monitoring_type) { $Agent.monitoring_type } else { "N/A" }
        'Data_Instalacao' = if ($Agent.install_time) { 
            [DateTime]::Parse($Agent.install_time).ToString("yyyy-MM-dd HH:mm:ss") 
        } else { "N/A" }
        'Observacoes' = if ($Agent.description) { $Agent.description } else { "" }
    }
}

# ============================================================================
# EXECUÇÃO PRINCIPAL
# ============================================================================

Write-Log "=== INICIANDO EXPORTAÇÃO DE INVENTÁRIO TACTICAL RMM ===" "SUCCESS"
Write-Log "Formato de saída: $OutputFormat"
Write-Log "Caminho de saída: $OutputPath"

try {
    # Buscar dados da API
    $agents = Get-TacticalAgents -ApiUrl $apiUrl -Token $ApiToken
    $clients = Get-TacticalClients -ApiUrl $apiUrl -Token $ApiToken
    $sites = Get-TacticalSites -ApiUrl $apiUrl -Token $ApiToken
    
    # Filtrar por cliente se especificado
    if ($ClientId) {
        $agents = $agents | Where-Object { $_.client -eq $ClientId }
        Write-Log "Filtrado para cliente ID: $ClientId ($($agents.Count) agentes)"
    }
    
    # Filtrar por site se especificado
    if ($SiteId) {
        $agents = $agents | Where-Object { $_.site -eq $SiteId }
        Write-Log "Filtrado para site ID: $SiteId ($($agents.Count) agentes)"
    }
    
    # Filtrar agentes offline se necessário
    if (-not $IncludeOffline) {
        $agents = $agents | Where-Object { 
            if ($_.last_seen) {
                $lastSeen = [DateTime]::Parse($_.last_seen)
                $timeDiff = (Get-Date) - $lastSeen
                $timeDiff.TotalHours -lt 24
            } else {
                $false
            }
        }
        Write-Log "Filtrado agentes offline ($($agents.Count) agentes restantes)"
    }
    
    if ($agents.Count -eq 0) {
        Write-Log "Nenhum agente encontrado com os filtros especificados" "WARN"
        exit 0
    }
    
    # Processar dados dos agentes
    Write-Log "Processando dados de $($agents.Count) agentes..."
    $inventoryData = @()
    
    foreach ($agent in $agents) {
        $formattedAgent = Format-AgentData -Agent $agent -Clients $clients -Sites $sites
        $inventoryData += $formattedAgent
    }
    
    # Gerar nome do arquivo
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $fileName = "TacticalRMM_Inventario_$timestamp"
    
    if ($ClientId) { $fileName += "_Cliente$ClientId" }
    if ($SiteId) { $fileName += "_Site$SiteId" }
    
    $filePath = Join-Path $OutputPath "$fileName.$($OutputFormat.ToLower())"
    
    # Exportar dados
    Write-Log "Exportando dados para: $filePath"
    
    if ($OutputFormat -eq 'Excel') {
        $inventoryData | Export-Excel -Path $filePath -AutoSize -AutoFilter -FreezeTopRow -BoldTopRow -WorksheetName "Inventario_Estacoes"
    } else {
        $inventoryData | Export-Csv -Path $filePath -NoTypeInformation -Encoding UTF8
    }
    
    Write-Log "Exportação concluída com sucesso!" "SUCCESS"
    Write-Log "Arquivo salvo: $filePath" "SUCCESS"
    Write-Log "Total de registros: $($inventoryData.Count)" "SUCCESS"
    
    # Estatísticas resumidas
    $onlineCount = ($inventoryData | Where-Object { $_.Status -eq "Online" }).Count
    $offlineCount = ($inventoryData | Where-Object { $_.Status -eq "Offline" }).Count
    $recentCount = ($inventoryData | Where-Object { $_.Status -eq "Recente" }).Count
    
    Write-Log "=== ESTATÍSTICAS ===" "SUCCESS"
    Write-Log "Online: $onlineCount | Recente: $recentCount | Offline: $offlineCount"
    
    # Mostrar distribuição por cliente
    $clientStats = $inventoryData | Group-Object Cliente | Sort-Object Count -Descending
    Write-Log "=== DISTRIBUIÇÃO POR CLIENTE ==="
    foreach ($stat in $clientStats) {
        Write-Log "$($stat.Name): $($stat.Count) estações"
    }

} catch {
    Write-Log "Erro durante a exportação: $($_.Exception.Message)" "ERROR"
    exit 1
}

Write-Log "=== EXPORTAÇÃO FINALIZADA ===" "SUCCESS"
