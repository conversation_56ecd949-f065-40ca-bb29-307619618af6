# 🚀 Guia de Configuração no Tactical RMM

Este guia mostra como configurar o script `Export-TacticalInventory-Email.ps1` no Tactical RMM Script Manager para execução automática.

## 📋 Pré-requisitos

1. ✅ Acesso administrativo ao Tactical RMM
2. ✅ Token de API configurado no script
3. ✅ Configurações SMTP válidas
4. ✅ IDs dos clientes que receberão relatórios

## 🔧 Passo 1: Adicionar Script no Tactical RMM

### 1.1 Acessar Script Manager
1. Faça login no Tactical RMM
2. Vá para **Settings** → **Script Manager**
3. Clique em **Add Script**

### 1.2 Configurar Script
Preencha os campos:

- **Name:** `Inventário por Email`
- **Description:** `Gera inventário do cliente e envia por email automaticamente`
- **Category:** `Inventory` (ou crie uma nova categoria)
- **Script Type:** `PowerShell`
- **Shell:** `powershell`
- **Timeout:** `300` (5 minutos)

### 1.3 Adicionar Código
<PERSON> o conteúdo completo do arquivo `Export-TacticalInventory-Email.ps1` no campo **Script**.

### 1.4 Salvar
Clique em **Save** para salvar o script.

## ⚙️ Passo 2: Configurar Argumentos

### 2.1 Argumentos Básicos
Para execução manual ou agendada, use o formato:
```
-ClientId 8 -EmailTo "<EMAIL>"
```

### 2.2 Exemplos de Argumentos

#### Cliente Sumire (ID 8):
```
-ClientId 8 -EmailTo "<EMAIL>" -EmailSubject "Relatório de Inventário Sumire"
```

#### Cliente NVirtual (ID 1):
```
-ClientId 1 -EmailTo "<EMAIL>" -EmailSubject "Inventário Interno NVirtual"
```

#### Múltiplos destinatários:
```
-ClientId 8 -EmailTo "<EMAIL>;<EMAIL>;<EMAIL>"
```

#### Excluindo estações offline:
```
-ClientId 8 -EmailTo "<EMAIL>" -IncludeOffline $false
```

#### Com configurações SMTP personalizadas:
```
-ClientId 8 -EmailTo "<EMAIL>" -SMTPServer "smtp.office365.com" -SMTPUser "<EMAIL>" -SMTPPassword "senha123"
```

## 🎯 Passo 3: Execução Manual

### 3.1 Executar em Agente Específico
1. Vá para **Agents**
2. Selecione qualquer agente do cliente
3. Clique em **Tasks** → **Run Script**
4. Selecione **Inventário por Email**
5. Adicione os argumentos necessários
6. Clique em **Run Now**

### 3.2 Monitorar Execução
- Acompanhe o progresso na aba **Tasks**
- Verifique os logs para detalhes da execução
- Confirme o recebimento do email

## 📅 Passo 4: Automação com Tasks

### 4.1 Criar Task Automática
1. Vá para **Automation Manager**
2. Clique em **Add Task**
3. Configure:
   - **Task Type:** `Run Script`
   - **Name:** `Relatório Semanal Cliente 8`
   - **Script:** `Inventário por Email`
   - **Arguments:** `-ClientId 8 -EmailTo "<EMAIL>"`

### 4.2 Configurar Agendamento
- **Schedule Type:** `Weekly`
- **Day of Week:** `Monday`
- **Time:** `08:00`
- **Timezone:** Selecione seu fuso horário

### 4.3 Definir Escopo
- **Run on:** `Any Agent` (ou selecione agentes específicos)
- **Client/Site Filter:** Configure conforme necessário

### 4.4 Salvar Task
Clique em **Save** para criar a task automática.

## 📊 Passo 5: Configurações Avançadas

### 5.1 Múltiplos Clientes
Crie tasks separadas para cada cliente:

**Task 1 - Cliente Sumire:**
```
Name: Relatório Sumire
Arguments: -ClientId 8 -EmailTo "<EMAIL>"
Schedule: Weekly, Monday, 08:00
```

**Task 2 - Cliente NVirtual:**
```
Name: Relatório NVirtual
Arguments: -ClientId 1 -EmailTo "<EMAIL>"
Schedule: Weekly, Monday, 08:30
```

### 5.2 Diferentes Frequências

**Relatório Diário (apenas online):**
```
Arguments: -ClientId 8 -EmailTo "<EMAIL>" -IncludeOffline $false -EmailSubject "Status Diário"
Schedule: Daily, 09:00
```

**Relatório Mensal Completo:**
```
Arguments: -ClientId 8 -EmailTo "<EMAIL>" -EmailSubject "Relatório Mensal Completo"
Schedule: Monthly, 1st day, 07:00
```

## 🔍 Passo 6: Monitoramento e Troubleshooting

### 6.1 Verificar Logs
1. **Task History** → Selecione a execução
2. Verifique **Output** para logs detalhados
3. Procure por mensagens de erro ou sucesso

### 6.2 Logs Típicos de Sucesso
```
[2025-01-03 10:30:15] [INFO] === INICIANDO GERAÇÃO DE INVENTÁRIO PARA EMAIL ===
[2025-01-03 10:30:16] [SUCCESS] Encontrados 45 registros de agentes
[2025-01-03 10:30:17] [SUCCESS] Cliente encontrado: Sumire
[2025-01-03 10:30:19] [SUCCESS] Email enviado com sucesso!
[2025-01-03 10:30:19] [SUCCESS] === RELATÓRIO ENVIADO COM SUCESSO ===
```

### 6.3 Problemas Comuns

**Erro: "Cliente não encontrado"**
- Verifique se o ClientId está correto
- Confirme se o cliente existe no Tactical RMM

**Erro: "Falha no envio de email"**
- Verifique configurações SMTP no script
- Teste credenciais de email manualmente
- Confirme conectividade com servidor SMTP

**Timeout do script**
- Aumente o timeout do script para 600 segundos
- Considere filtrar apenas estações online para reduzir processamento

## 📧 Passo 7: Configuração de Email

### 7.1 Gmail/Google Workspace
1. Ative autenticação de 2 fatores
2. Gere senha de aplicativo
3. Use a senha de aplicativo no script

### 7.2 Office 365/Outlook
1. Configure autenticação moderna se necessário
2. Use credenciais da conta de serviço
3. Verifique políticas de segurança

### 7.3 Servidor SMTP Personalizado
1. Confirme configurações de porta e SSL
2. Teste conectividade com telnet
3. Verifique firewall e políticas de rede

## ✅ Checklist Final

- [ ] Script adicionado ao Tactical RMM
- [ ] Argumentos configurados corretamente
- [ ] Teste manual executado com sucesso
- [ ] Email recebido e formatação verificada
- [ ] Tasks automáticas criadas
- [ ] Agendamento configurado
- [ ] Monitoramento implementado
- [ ] Documentação atualizada para equipe

## 🆘 Suporte

Para problemas ou dúvidas:
1. Verifique logs detalhados no Tactical RMM
2. Consulte `README-Email-Script.md` para documentação completa
3. Execute `Test-EmailScript.ps1` para testes locais
4. Entre em contato com a equipe NVirtual

---

**Configuração realizada por:** _______________  
**Data:** _______________  
**Testado e aprovado por:** _______________
