# 📊 Tactical Inventory Export

Sistema completo para exportação de inventário de estações do Tactical RMM para controle e auditoria.

## 🚀 Início Rápido

### 1. Configuração Automática
```powershell
# Executar setup completo
.\Setup-TacticalInventory.ps1 -All

# Ou configurar manualmente
.\Setup-TacticalInventory.ps1 -InstallModules -CreateFolders -SetupConfig
```

### 2. Configurar Token de API
```powershell
# Definir token como variável de ambiente (recomendado)
$env:TACTICAL_RMM_TOKEN = "SEU_TOKEN_AQUI"
```

### 3. Testar Conectividade
```powershell
# Testar todos os componentes
.\Test-TacticalInventory.ps1 -TestMode all
```

### 4. Primeira Exportação
```powershell
# Exportação básica
.\Export-TacticalInventory.ps1

# Exportação avançada com hardware
.\Export-TacticalInventory-Advanced.ps1 -IncludeHardware -IncludeSoftware
```

## 📁 Estrutura do Projeto

```
Tactical-Inventory-Export/
├── 📄 README.md                           # Este arquivo
├── 📄 README-Export-Inventory.md          # Documentação detalhada
├── 🔧 Setup-TacticalInventory.ps1         # Script de instalação/configuração
├── 🧪 Test-TacticalInventory.ps1          # Script de testes
├── 📊 Export-TacticalInventory.ps1        # Exportação básica
├── 📊 Export-TacticalInventory-Advanced.ps1 # Exportação avançada
├── ⚙️  config.example.ps1                 # Exemplo de configuração
└── ⚙️  config.ps1                         # Configuração personalizada (criado pelo setup)
```

## 🛠️ Scripts Disponíveis

### 📊 Export-TacticalInventory.ps1
**Exportação básica de inventário**
- Dados essenciais de todas as estações
- Filtros por cliente e site
- Formatos Excel e CSV
- Informações: hostname, cliente, site, status, SO, hardware básico

**Exemplos:**
```powershell
# Exportar todos os clientes
.\Export-TacticalInventory.ps1

# Cliente específico em CSV
.\Export-TacticalInventory.ps1 -ClientId 8 -OutputFormat CSV

# Site específico
.\Export-TacticalInventory.ps1 -SiteId 54
```

### 📊 Export-TacticalInventory-Advanced.ps1
**Exportação avançada com detalhes de hardware**
- Informações detalhadas de hardware via WMI
- Lista de software instalado
- Custom fields configurados
- Excel com múltiplas abas

**Exemplos:**
```powershell
# Exportação completa
.\Export-TacticalInventory-Advanced.ps1 -IncludeHardware -IncludeSoftware -IncludeCustomFields

# Apenas hardware para cliente específico
.\Export-TacticalInventory-Advanced.ps1 -ClientId 8 -IncludeHardware
```

### 🧪 Test-TacticalInventory.ps1
**Testes e validação**
- Testa conectividade com API
- Valida funcionamento dos scripts
- Verifica pré-requisitos

**Exemplos:**
```powershell
# Teste completo
.\Test-TacticalInventory.ps1 -TestMode all

# Apenas conectividade
.\Test-TacticalInventory.ps1 -TestMode connectivity
```

### 🔧 Setup-TacticalInventory.ps1
**Instalação e configuração**
- Instala módulos necessários
- Cria estrutura de pastas
- Configura arquivos
- Testa conectividade

**Exemplos:**
```powershell
# Setup completo
.\Setup-TacticalInventory.ps1 -All

# Apenas instalar módulos
.\Setup-TacticalInventory.ps1 -InstallModules
```

## 📋 Pré-requisitos

- **PowerShell 5.1+**
- **Token de API do Tactical RMM** com permissões de leitura
- **Módulo ImportExcel** (instalado automaticamente pelo setup)
- **Conectividade** com `api.centralmesh.nvirtual.com.br`

## ⚙️ Configuração

### Token de API
1. Acesse Tactical RMM → Settings → Global Settings → API Keys
2. Crie nova API Key com permissões de leitura
3. Configure como variável de ambiente:
```powershell
$env:TACTICAL_RMM_TOKEN = "SEU_TOKEN_AQUI"
```

### Personalização
Edite `config.ps1` para personalizar:
- Pastas de saída
- Formatos padrão
- Mapeamentos de clientes/sites
- Configurações de performance

## 📊 Dados Exportados

### Exportação Básica
- Informações gerais da estação
- Status de conectividade
- Sistema operacional
- Hardware básico (CPU, RAM, disco)
- Informações de rede
- Status de serviços e checks

### Exportação Avançada
- **Hardware detalhado**: CPU, RAM, discos, placa-mãe, BIOS
- **Software instalado**: Lista completa com versões
- **Custom fields**: Campos personalizados do Tactical RMM
- **Múltiplas abas** no Excel para organização

## 🎯 Casos de Uso

### 📋 Auditoria de Inventário
```powershell
# Relatório mensal completo
.\Export-TacticalInventory.ps1 -OutputPath "C:\Auditorias\$(Get-Date -Format 'yyyy-MM')\"
```

### 🏢 Relatório por Cliente
```powershell
# Relatório detalhado para cliente específico
.\Export-TacticalInventory-Advanced.ps1 -ClientId 8 -IncludeHardware -IncludeSoftware
```

### 💻 Controle de Hardware
```powershell
# Inventário de hardware para planejamento
.\Export-TacticalInventory-Advanced.ps1 -IncludeHardware
```

### 📦 Auditoria de Software
```powershell
# Lista de software para compliance
.\Export-TacticalInventory-Advanced.ps1 -IncludeSoftware
```

## 🔧 Solução de Problemas

### ❌ Erro de Token
```
Erro: Unauthorized
```
**Solução:** Verificar token e permissões na API

### ❌ Módulo não encontrado
```
Módulo ImportExcel não encontrado
```
**Solução:** Executar `.\Setup-TacticalInventory.ps1 -InstallModules`

### ❌ Timeout na API
```
Erro: The operation has timed out
```
**Solução:** Verificar conectividade ou usar filtros para reduzir carga

## 📅 Automação

### Tarefa Agendada Semanal
```powershell
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\TacticalInventory\Export-TacticalInventory.ps1"
$trigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Monday -At 6AM
Register-ScheduledTask -TaskName "Tactical Inventory Export" -Action $action -Trigger $trigger
```

## 📖 Documentação Adicional

- **README-Export-Inventory.md** - Documentação completa e detalhada
- **config.example.ps1** - Todas as opções de configuração disponíveis

## 🆘 Suporte

Para problemas, melhorias ou dúvidas:
- Consulte a documentação detalhada
- Execute os testes para diagnóstico
- Entre em contato com a equipe NVirtual

## 📝 Changelog

### v1.0 (2025-01-02)
- ✅ Exportação básica de inventário
- ✅ Exportação avançada com hardware/software
- ✅ Sistema de testes e validação
- ✅ Setup automatizado
- ✅ Configuração flexível
- ✅ Documentação completa

---

**Desenvolvido por NVirtual** 🚀
