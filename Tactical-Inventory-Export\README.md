# 📊 Tactical Inventory Export

Sistema completo para exportação de inventário de estações do Tactical RMM para controle e auditoria.

## 🚀 Início Rápido

### 1. Configuração Automática
```powershell
# Executar setup completo
.\Setup-TacticalInventory.ps1 -All

# Ou configurar manualmente
.\Setup-TacticalInventory.ps1 -InstallModules -CreateFolders -SetupConfig
```

### 2. Configurar Token de API
```powershell
# Definir token como variável de ambiente (recomendado)
$env:TACTICAL_RMM_TOKEN = "SEU_TOKEN_AQUI"
```

### 3. Testar Conectividade
```powershell
# Testar todos os componentes
.\Test-TacticalInventory.ps1 -TestMode all
```

### 4. Primeira Exportação
```powershell
# Exportação básica
.\Export-TacticalInventory.ps1

# Exportação avançada com hardware
.\Export-TacticalInventory-Advanced.ps1 -IncludeHardware -IncludeSoftware
```

## 📁 Estrutura do Projeto

```
Tactical-Inventory-Export/
├── 📄 README.md                           # Este arquivo
├── 📄 README-Export-Inventory.md          # Documentação detalhada
├── 📄 README-Email-Script.md              # Documentação do script de email
├── 📄 README-Ubuntu-Server.md             # Documentação para Ubuntu Server
├── 🔧 Setup-TacticalInventory.ps1         # Script de instalação/configuração (Windows)
├── 🔧 Setup-Ubuntu-Server.sh              # Script de instalação para Ubuntu Server
├── 🔧 setup-cron-jobs.sh                  # Configuração de cron jobs (Linux)
├── 🧪 Test-TacticalInventory.ps1          # Script de testes (Windows)
├── 🧪 Test-EmailScript.ps1                # Teste do script de email (Windows)
├── 🧪 Test-Linux-Script.ps1               # Teste para Ubuntu Server
├── 📊 Export-TacticalInventory.ps1        # Exportação básica (Windows)
├── 📊 Export-TacticalInventory-Advanced.ps1 # Exportação avançada (Windows)
├── 📧 Export-TacticalInventory-Email.ps1  # Exportação com envio por email (Windows)
├── 🐧 Export-TacticalInventory-Linux.ps1  # Exportação para Ubuntu Server com anexo Excel
├── ⚙️  config.example.ps1                 # Exemplo de configuração (Windows)
├── ⚙️  config-email.example.ps1           # Configuração para script de email
└── ⚙️  config.ps1                         # Configuração personalizada (criado pelo setup)
```

## 🛠️ Scripts Disponíveis

### 📊 Export-TacticalInventory.ps1
**Exportação básica de inventário**
- Dados essenciais de todas as estações
- Filtros por cliente e site
- Formatos Excel e CSV
- Informações: hostname, cliente, site, status, SO, hardware básico

**Exemplos:**
```powershell
# Exportar todos os clientes
.\Export-TacticalInventory.ps1

# Cliente específico em CSV
.\Export-TacticalInventory.ps1 -ClientId 8 -OutputFormat CSV

# Site específico
.\Export-TacticalInventory.ps1 -SiteId 54
```

### 📊 Export-TacticalInventory-Advanced.ps1
**Exportação avançada com detalhes de hardware**
- Informações detalhadas de hardware via WMI
- Lista de software instalado
- Custom fields configurados
- Excel com múltiplas abas

**Exemplos:**
```powershell
# Exportação completa
.\Export-TacticalInventory-Advanced.ps1 -IncludeHardware -IncludeSoftware -IncludeCustomFields

# Apenas hardware para cliente específico
.\Export-TacticalInventory-Advanced.ps1 -ClientId 8 -IncludeHardware
```

### 🧪 Test-TacticalInventory.ps1
**Testes e validação**
- Testa conectividade com API
- Valida funcionamento dos scripts
- Verifica pré-requisitos

**Exemplos:**
```powershell
# Teste completo
.\Test-TacticalInventory.ps1 -TestMode all

# Apenas conectividade
.\Test-TacticalInventory.ps1 -TestMode connectivity
```

### 📧 Export-TacticalInventory-Email.ps1 (Windows)
**Exportação com envio automático por email**
- Otimizado para Tactical RMM Script Manager
- Gera inventário de cliente específico
- Organiza estações por site
- Envia relatório HTML formatado por email
- Execução via argumentos do Tactical RMM

**Exemplos:**
```powershell
# Uso no Tactical RMM (argumentos)
-ClientId 8 -EmailTo "<EMAIL>"

# Com assunto personalizado
-ClientId 8 -EmailTo "<EMAIL>" -EmailSubject "Relatório Mensal"

# Excluindo estações offline
-ClientId 8 -EmailTo "<EMAIL>" -IncludeOffline $false
```

### 🐧 Export-TacticalInventory-Linux.ps1 (Ubuntu Server)
**Exportação para Ubuntu Server com anexo Excel**
- Otimizado para PowerShell Core no Linux
- Gera arquivo Excel com múltiplas abas
- Envia email HTML com anexo Excel automático
- Execução via cron jobs ou manual
- Logs coloridos e limpeza automática de arquivos temporários

**Exemplos:**
```bash
# Execução direta
pwsh ./Export-TacticalInventory-Linux.ps1 -ClientId 8 -EmailTo "<EMAIL>"

# Com configurações SMTP personalizadas
pwsh ./Export-TacticalInventory-Linux.ps1 \
  -ClientId 8 -EmailTo "<EMAIL>" \
  -SMTPServer "smtp.office365.com" -SMTPUser "<EMAIL>"

# Usando script wrapper
tactical-inventory -ClientId 8 -EmailTo "<EMAIL>"
```

### 🔧 Setup-TacticalInventory.ps1
**Instalação e configuração**
- Instala módulos necessários
- Cria estrutura de pastas
- Configura arquivos
- Testa conectividade

**Exemplos:**
```powershell
# Setup completo
.\Setup-TacticalInventory.ps1 -All

# Apenas instalar módulos
.\Setup-TacticalInventory.ps1 -InstallModules
```

## 📋 Pré-requisitos

### Windows
- **PowerShell 5.1+**
- **Token de API do Tactical RMM** com permissões de leitura
- **Módulo ImportExcel** (instalado automaticamente pelo setup)
- **Conectividade** com `api.centralmesh.nvirtual.com.br`

### Ubuntu Server
- **Ubuntu Server 20.04+**
- **PowerShell Core (pwsh)** 7.0+
- **Módulo ImportExcel** para PowerShell Core
- **Conectividade SMTP** para envio de emails
- **Permissões de escrita** em /tmp e /var/log

## ⚙️ Configuração

### Token de API
1. Acesse Tactical RMM → Settings → Global Settings → API Keys
2. Crie nova API Key com permissões de leitura
3. Configure como variável de ambiente:
```powershell
$env:TACTICAL_RMM_TOKEN = "SEU_TOKEN_AQUI"
```

### Personalização
Edite `config.ps1` para personalizar:
- Pastas de saída
- Formatos padrão
- Mapeamentos de clientes/sites
- Configurações de performance

## 📊 Dados Exportados

### Exportação Básica
- Informações gerais da estação
- Status de conectividade
- Sistema operacional
- Hardware básico (CPU, RAM, disco)
- Informações de rede
- Status de serviços e checks

### Exportação Avançada
- **Hardware detalhado**: CPU, RAM, discos, placa-mãe, BIOS
- **Software instalado**: Lista completa com versões
- **Custom fields**: Campos personalizados do Tactical RMM
- **Múltiplas abas** no Excel para organização

## 🎯 Casos de Uso

### 📧 Relatórios Automáticos por Email (Tactical RMM)
```powershell
# Relatório diário automático
# Configurar no Tactical RMM Script Manager:
# Argumentos: -ClientId 8 -EmailTo "<EMAIL>"

# Relatório semanal com assunto personalizado
# Argumentos: -ClientId 8 -EmailTo "<EMAIL>" -EmailSubject "Relatório Semanal"

# Monitoramento apenas de estações ativas
# Argumentos: -ClientId 8 -EmailTo "<EMAIL>" -IncludeOffline $false
```

### 📋 Auditoria de Inventário
```powershell
# Relatório mensal completo
.\Export-TacticalInventory.ps1 -OutputPath "C:\Auditorias\$(Get-Date -Format 'yyyy-MM')\"
```

### 🏢 Relatório por Cliente
```powershell
# Relatório detalhado para cliente específico
.\Export-TacticalInventory-Advanced.ps1 -ClientId 8 -IncludeHardware -IncludeSoftware
```

### 💻 Controle de Hardware
```powershell
# Inventário de hardware para planejamento
.\Export-TacticalInventory-Advanced.ps1 -IncludeHardware
```

### 📦 Auditoria de Software
```powershell
# Lista de software para compliance
.\Export-TacticalInventory-Advanced.ps1 -IncludeSoftware
```

## 🔧 Solução de Problemas

### ❌ Erro de Token
```
Erro: Unauthorized
```
**Solução:** Verificar token e permissões na API

### ❌ Módulo não encontrado
```
Módulo ImportExcel não encontrado
```
**Solução:** Executar `.\Setup-TacticalInventory.ps1 -InstallModules`

### ❌ Timeout na API
```
Erro: The operation has timed out
```
**Solução:** Verificar conectividade ou usar filtros para reduzir carga

## 📅 Automação

### 🚀 Automação via Tactical RMM (Windows)

#### Configurar Script no Tactical RMM:
1. **Settings → Script Manager → Add Script**
2. **Name:** `Inventário por Email`
3. **Script Type:** `PowerShell`
4. **Cole o conteúdo de:** `Export-TacticalInventory-Email.ps1`

#### Executar Manualmente:
- **Agents → Selecionar Agente → Tasks → Run Script**
- **Argumentos:** `-ClientId 8 -EmailTo "<EMAIL>"`

#### Agendar Execução Automática:
- **Automation Manager → Add Task**
- **Task Type:** `Run Script`
- **Script:** `Inventário por Email`
- **Arguments:** `-ClientId 8 -EmailTo "<EMAIL>"`
- **Schedule:** Configurar conforme necessário

### 🐧 Automação no Ubuntu Server (RECOMENDADO)

#### Instalação Rápida:
```bash
# Executar setup completo
chmod +x Setup-Ubuntu-Server.sh
./Setup-Ubuntu-Server.sh

# Configurar cron jobs
chmod +x setup-cron-jobs.sh
./setup-cron-jobs.sh
```

#### Cron Jobs Manuais:
```bash
# Editar crontab
crontab -e

# Relatório diário às 8h
0 8 * * * /usr/local/bin/tactical-inventory -ClientId 8 -EmailTo "<EMAIL>" >> /var/log/tactical-inventory/daily.log 2>&1

# Relatório semanal às segundas 7h
0 7 * * 1 /usr/local/bin/tactical-inventory -ClientId 8 -EmailTo "<EMAIL>" -EmailSubject "Relatório Semanal" >> /var/log/tactical-inventory/weekly.log 2>&1
```

### Tarefa Agendada Local (Windows)
```powershell
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\TacticalInventory\Export-TacticalInventory.ps1"
$trigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Monday -At 6AM
Register-ScheduledTask -TaskName "Tactical Inventory Export" -Action $action -Trigger $trigger
```

## 📖 Documentação Adicional

- **README-Export-Inventory.md** - Documentação completa e detalhada (Windows)
- **README-Email-Script.md** - Guia completo do script de email para Tactical RMM (Windows)
- **README-Ubuntu-Server.md** - Guia completo para Ubuntu Server com PowerShell Core
- **config.example.ps1** - Todas as opções de configuração disponíveis (Windows)
- **config-email.example.ps1** - Configurações específicas para envio de email

## 🆘 Suporte

Para problemas, melhorias ou dúvidas:
- Consulte a documentação detalhada
- Execute os testes para diagnóstico
- Entre em contato com a equipe NVirtual

## 📝 Changelog

### v1.2 (2025-01-03)
- ✅ **NOVO:** Versão completa para Ubuntu Server (Export-TacticalInventory-Linux.ps1)
- ✅ **NOVO:** Anexo Excel automático com múltiplas abas por site
- ✅ **NOVO:** Setup automatizado para Ubuntu Server (Setup-Ubuntu-Server.sh)
- ✅ **NOVO:** Configuração automática de cron jobs (setup-cron-jobs.sh)
- ✅ **NOVO:** Script de testes específico para Linux (Test-Linux-Script.ps1)
- ✅ **NOVO:** Logs coloridos e limpeza automática de arquivos temporários
- ✅ **NOVO:** Documentação completa para Ubuntu Server
- ✅ **NOVO:** Script wrapper para execução simplificada

### v1.1 (2025-01-03)
- ✅ **NOVO:** Script para envio automático por email (Export-TacticalInventory-Email.ps1)
- ✅ **NOVO:** Otimização para Tactical RMM Script Manager
- ✅ **NOVO:** Relatórios HTML formatados com estatísticas visuais
- ✅ **NOVO:** Organização automática por sites do cliente
- ✅ **NOVO:** Sistema de configuração específico para email
- ✅ **NOVO:** Script de testes para funcionalidade de email
- ✅ **NOVO:** Documentação completa para uso no Tactical RMM

### v1.0 (2025-01-02)
- ✅ Exportação básica de inventário
- ✅ Exportação avançada com hardware/software
- ✅ Sistema de testes e validação
- ✅ Setup automatizado
- ✅ Configuração flexível
- ✅ Documentação completa

---

**Desenvolvido por NVirtual** 🚀
