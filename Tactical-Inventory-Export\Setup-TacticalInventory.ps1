<#
.SYNOPSIS
    Script de instalação e configuração do Tactical Inventory Export

.DESCRIPTION
    Este script configura o ambiente necessário para executar os scripts
    de exportação de inventário do Tactical RMM, incluindo instalação
    de módulos, criação de pastas e configuração inicial.

.PARAMETER InstallModules
    Instala módulos PowerShell necessários

.PARAMETER CreateFolders
    Cria estrutura de pastas necessárias

.PARAMETER SetupConfig
    Configura arquivo de configuração

.PARAMETER TestConnection
    Testa conectividade com a API

.PARAMETER All
    Executa todas as etapas de configuração

.EXAMPLE
    .\Setup-TacticalInventory.ps1 -All
    Executa configuração completa

.EXAMPLE
    .\Setup-TacticalInventory.ps1 -InstallModules -TestConnection
    Instala módulos e testa conectividade

.NOTES
    Versão: 1.0
    Autor: NVirtual
    Data: 2025-01-02
#>

param(
    [switch]$InstallModules,
    [switch]$CreateFolders,
    [switch]$SetupConfig,
    [switch]$TestConnection,
    [switch]$All,
    [string]$ApiToken = $env:TACTICAL_RMM_TOKEN
)

# Se -All foi especificado, ativar todas as opções
if ($All) {
    $InstallModules = $true
    $CreateFolders = $true
    $SetupConfig = $true
    $TestConnection = $true
}

# Se nenhuma opção foi especificada, mostrar ajuda
if (-not ($InstallModules -or $CreateFolders -or $SetupConfig -or $TestConnection)) {
    Write-Host @"
=== SETUP TACTICAL INVENTORY EXPORT ===

Este script configura o ambiente para exportação de inventário do Tactical RMM.

Opções disponíveis:
  -InstallModules    Instala módulos PowerShell necessários
  -CreateFolders     Cria estrutura de pastas
  -SetupConfig       Configura arquivo de configuração
  -TestConnection    Testa conectividade com a API
  -All               Executa todas as etapas

Exemplos:
  .\Setup-TacticalInventory.ps1 -All
  .\Setup-TacticalInventory.ps1 -InstallModules -TestConnection

Para mais informações, consulte README-Export-Inventory.md
"@ -ForegroundColor Cyan
    exit 0
}

function Write-SetupLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    
    switch ($Level) {
        "SUCCESS" { Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red }
        "WARN"    { Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "[$timestamp] ℹ️  $Message" -ForegroundColor Cyan }
        default   { Write-Host "[$timestamp] 📝 $Message" }
    }
}

function Install-RequiredModules {
    Write-SetupLog "=== INSTALANDO MÓDULOS NECESSÁRIOS ===" "INFO"
    
    $modules = @(
        @{Name="ImportExcel"; Description="Para exportação em formato Excel"}
    )
    
    foreach ($module in $modules) {
        Write-SetupLog "Verificando módulo $($module.Name)..."
        
        $installed = Get-Module $module.Name -ListAvailable
        if ($installed) {
            $version = $installed[0].Version
            Write-SetupLog "$($module.Name) já está instalado (v$version)" "SUCCESS"
        } else {
            Write-SetupLog "Instalando $($module.Name)..." "INFO"
            try {
                Install-Module $module.Name -Scope CurrentUser -Force -AllowClobber
                Write-SetupLog "$($module.Name) instalado com sucesso!" "SUCCESS"
            } catch {
                Write-SetupLog "Erro ao instalar $($module.Name): $($_.Exception.Message)" "ERROR"
                return $false
            }
        }
    }
    
    return $true
}

function Create-FolderStructure {
    Write-SetupLog "=== CRIANDO ESTRUTURA DE PASTAS ===" "INFO"
    
    $folders = @(
        "C:\TacticalInventory",
        "C:\TacticalInventory\Exports",
        "C:\TacticalInventory\Logs",
        "C:\TacticalInventory\Config",
        "C:\TacticalInventory\Temp"
    )
    
    foreach ($folder in $folders) {
        if (-not (Test-Path $folder)) {
            try {
                New-Item -Path $folder -ItemType Directory -Force | Out-Null
                Write-SetupLog "Pasta criada: $folder" "SUCCESS"
            } catch {
                Write-SetupLog "Erro ao criar pasta $folder - $($_.Exception.Message)" "ERROR"
                return $false
            }
        } else {
            Write-SetupLog "Pasta já existe: $folder" "INFO"
        }
    }
    
    return $true
}

function Setup-Configuration {
    Write-SetupLog "=== CONFIGURANDO ARQUIVO DE CONFIGURAÇÃO ===" "INFO"
    
    $configPath = "config.ps1"
    $examplePath = "config.example.ps1"
    
    if (-not (Test-Path $examplePath)) {
        Write-SetupLog "Arquivo config.example.ps1 não encontrado" "ERROR"
        return $false
    }
    
    if (Test-Path $configPath) {
        Write-SetupLog "Arquivo config.ps1 já existe" "INFO"
        $response = Read-Host "Deseja sobrescrever? (s/N)"
        if ($response -ne 's' -and $response -ne 'S') {
            Write-SetupLog "Configuração mantida" "INFO"
            return $true
        }
    }
    
    try {
        Copy-Item $examplePath $configPath
        Write-SetupLog "Arquivo config.ps1 criado com sucesso!" "SUCCESS"
        Write-SetupLog "Edite o arquivo config.ps1 para personalizar as configurações" "INFO"
        
        # Verificar se token está configurado
        if (-not $ApiToken) {
            Write-SetupLog "IMPORTANTE: Configure o token de API!" "WARN"
            Write-SetupLog "Execute: \$env:TACTICAL_RMM_TOKEN = 'SEU_TOKEN_AQUI'" "INFO"
        }
        
        return $true
    } catch {
        Write-SetupLog "Erro ao criar config.ps1: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-APIConnection {
    Write-SetupLog "=== TESTANDO CONECTIVIDADE COM A API ===" "INFO"
    
    if (-not $ApiToken) {
        Write-SetupLog "Token de API não configurado" "ERROR"
        Write-SetupLog "Configure com: \$env:TACTICAL_RMM_TOKEN = 'SEU_TOKEN_AQUI'" "INFO"
        return $false
    }
    
    $apiUrl = "https://api.centralmesh.nvirtual.com.br"
    
    try {
        Write-SetupLog "Testando conexão com $apiUrl..."
        
        $response = Invoke-RestMethod -Uri "$apiUrl/agents/" -Headers @{
            "X-API-KEY" = $ApiToken
        } -TimeoutSec 10
        
        Write-SetupLog "Conectividade OK!" "SUCCESS"
        Write-SetupLog "Encontrados $($response.Count) agentes na API" "SUCCESS"
        
        # Testar outros endpoints
        $endpoints = @("clients", "clients/sites")
        foreach ($endpoint in $endpoints) {
            try {
                $result = Invoke-RestMethod -Uri "$apiUrl/$endpoint/" -Headers @{
                    "X-API-KEY" = $ApiToken
                } -TimeoutSec 10
                Write-SetupLog "Endpoint $endpoint OK" "SUCCESS"
            } catch {
                Write-SetupLog "Endpoint $endpoint ERRO - $($_.Exception.Message)" "WARN"
            }
        }
        
        return $true
        
    } catch {
        Write-SetupLog "Falha na conectividade: $($_.Exception.Message)" "ERROR"
        
        if ($_.Exception.Message -like "*Unauthorized*") {
            Write-SetupLog "Token de API inválido ou sem permissões" "ERROR"
        } elseif ($_.Exception.Message -like "*timeout*") {
            Write-SetupLog "Timeout na conexão - verifique conectividade de rede" "ERROR"
        }
        
        return $false
    }
}

function Show-NextSteps {
    Write-SetupLog "=== PRÓXIMOS PASSOS ===" "INFO"
    Write-Host @"

🎉 Configuração concluída! Próximos passos:

1. 📝 Editar configurações (opcional):
   notepad config.ps1

2. 🧪 Testar scripts:
   .\Test-TacticalInventory.ps1 -TestMode all

3. 📊 Executar primeira exportação:
   .\Export-TacticalInventory.ps1 -VerboseOutput

4. 📊 Exportação avançada:
   .\Export-TacticalInventory-Advanced.ps1 -IncludeHardware

5. 📖 Consultar documentação:
   README-Export-Inventory.md

🔗 Arquivos importantes:
   - Export-TacticalInventory.ps1 (exportação básica)
   - Export-TacticalInventory-Advanced.ps1 (exportação avançada)
   - Test-TacticalInventory.ps1 (testes)
   - config.ps1 (configurações)
   - README-Export-Inventory.md (documentação)

"@ -ForegroundColor Green
}

# ============================================================================
# EXECUÇÃO PRINCIPAL
# ============================================================================

Write-SetupLog "=== INICIANDO SETUP TACTICAL INVENTORY EXPORT ===" "SUCCESS"

$allSuccess = $true

# Verificar privilégios
if (-not ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-SetupLog "Executando sem privilégios de administrador" "WARN"
    Write-SetupLog "Algumas operações podem falhar" "WARN"
}

# Executar etapas conforme solicitado
if ($InstallModules) {
    $allSuccess = $allSuccess -and (Install-RequiredModules)
}

if ($CreateFolders) {
    $allSuccess = $allSuccess -and (Create-FolderStructure)
}

if ($SetupConfig) {
    $allSuccess = $allSuccess -and (Setup-Configuration)
}

if ($TestConnection) {
    $allSuccess = $allSuccess -and (Test-APIConnection)
}

# Resultado final
Write-SetupLog "=== RESULTADO DO SETUP ===" "INFO"
if ($allSuccess) {
    Write-SetupLog "Setup concluído com sucesso!" "SUCCESS"
    Show-NextSteps
} else {
    Write-SetupLog "Setup concluído com alguns erros" "WARN"
    Write-SetupLog "Verifique as mensagens acima e corrija os problemas" "INFO"
}

Write-SetupLog "=== SETUP FINALIZADO ===" "INFO"
