# Exportação de Inventário Tactical RMM

Este conjunto de scripts permite exportar dados de inventário das estações gerenciadas pelo Tactical RMM para controle e auditoria.

## 📋 Scripts Disponíveis

### 1. Export-TacticalInventory.ps1
Script básico para exportação de inventário com informações essenciais das estações.

**Características:**
- Exporta dados básicos de todas as estações
- Suporte a filtros por cliente e site
- Formatos de saída: Excel e CSV
- Informações incluídas: hostname, cliente, site, status, SO, hardware básico

### 2. Export-TacticalInventory-Advanced.ps1
Script avançado para exportação detalhada com informações de hardware e software.

**Características:**
- Exporta dados detalhados de hardware via WMI
- Lista software instalado em cada estação
- Inclui custom fields configurados
- Saída em Excel com múltiplas abas
- Informações detalhadas: CPU, RAM, discos, placa-mãe, BIOS, etc.

## 🚀 Pré-requisitos

### PowerShell
- PowerShell 5.1 ou superior
- Acesso à API do Tactical RMM
- Token de API com permissões de leitura

### Módulos PowerShell
```powershell
# Para exportação Excel (recomendado)
Install-Module ImportExcel -Scope CurrentUser

# Verificar se está instalado
Get-Module ImportExcel -ListAvailable
```

### Token de API
1. Acesse o Tactical RMM via web
2. Vá em **Settings → Global Settings → API Keys**
3. Crie uma nova API Key com permissões de leitura
4. Configure como variável de ambiente:
```powershell
$env:TACTICAL_RMM_TOKEN = "SEU_TOKEN_AQUI"
```

## 📊 Uso dos Scripts

### Exportação Básica

```powershell
# Exportar todos os clientes para Excel
.\Export-TacticalInventory.ps1

# Exportar cliente específico para CSV
.\Export-TacticalInventory.ps1 -ClientId 8 -OutputFormat CSV

# Exportar site específico
.\Export-TacticalInventory.ps1 -SiteId 54

# Exportar para pasta específica
.\Export-TacticalInventory.ps1 -OutputPath "C:\Inventario\"

# Excluir agentes offline
.\Export-TacticalInventory.ps1 -IncludeOffline $false

# Modo verbose para debugging
.\Export-TacticalInventory.ps1 -VerboseOutput
```

### Exportação Avançada

```powershell
# Exportar com informações de hardware
.\Export-TacticalInventory-Advanced.ps1 -IncludeHardware

# Exportar com lista de software
.\Export-TacticalInventory-Advanced.ps1 -IncludeSoftware

# Exportar com custom fields
.\Export-TacticalInventory-Advanced.ps1 -IncludeCustomFields

# Exportação completa para cliente específico
.\Export-TacticalInventory-Advanced.ps1 -ClientId 8 -IncludeHardware -IncludeSoftware -IncludeCustomFields

# Especificar pasta de saída
.\Export-TacticalInventory-Advanced.ps1 -OutputPath "C:\Inventario\" -IncludeHardware
```

## 📁 Estrutura dos Arquivos Gerados

### Exportação Básica
**Arquivo:** `TacticalRMM_Inventario_YYYYMMDD_HHMMSS.xlsx` ou `.csv`

**Colunas incluídas:**
- ID_Agente
- Hostname
- Cliente
- Site
- Status (Online/Offline/Recente)
- Sistema_Operacional
- Versao_OS
- Arquitetura
- IP_Publico
- Agente_Versao
- Ultimo_Contato
- Tempo_Boot
- CPU_Modelo
- RAM_Total_GB
- Espaco_Disco_GB
- Antivirus
- Dominio
- Usuario_Logado
- Servicos_Falhas
- Checks_Falhas
- Manutencao
- Monitoramento
- Data_Instalacao
- Observacoes

### Exportação Avançada
**Arquivo:** `TacticalRMM_Inventario_Avancado_YYYYMMDD_HHMMSS.xlsx`

**Abas do Excel:**
1. **Inventario_Basico** - Dados essenciais de cada estação
2. **Hardware_Detalhado** - Informações detalhadas de hardware (CPU, RAM, discos, placa-mãe, BIOS)
3. **Software_Instalado** - Lista completa de software instalado
4. **Custom_Fields** - Campos customizados configurados no Tactical RMM

## 🔧 Configuração

### IDs de Clientes e Sites
Os scripts detectam automaticamente os IDs através da API. Para verificar os IDs disponíveis:

```powershell
# Listar clientes
$clients = Invoke-RestMethod -Uri "https://api.centralmesh.nvirtual.com.br/clients/" -Headers @{"X-API-KEY" = $env:TACTICAL_RMM_TOKEN}
$clients | Select-Object id, name

# Listar sites
$sites = Invoke-RestMethod -Uri "https://api.centralmesh.nvirtual.com.br/clients/sites/" -Headers @{"X-API-KEY" = $env:TACTICAL_RMM_TOKEN}
$sites | Select-Object id, name, client
```

### Personalização
Para personalizar os campos exportados, edite a função `Format-AgentData` nos scripts.

## 📈 Casos de Uso

### 1. Auditoria de Inventário
```powershell
# Exportar inventário completo mensal
.\Export-TacticalInventory.ps1 -OutputPath "C:\Auditorias\$(Get-Date -Format 'yyyy-MM')\"
```

### 2. Relatório por Cliente
```powershell
# Gerar relatório detalhado para cliente específico
.\Export-TacticalInventory-Advanced.ps1 -ClientId 8 -IncludeHardware -IncludeSoftware -OutputPath "C:\Relatorios\Cliente8\"
```

### 3. Monitoramento de Hardware
```powershell
# Exportar apenas informações de hardware
.\Export-TacticalInventory-Advanced.ps1 -IncludeHardware -OutputPath "C:\Hardware\"
```

### 4. Controle de Software
```powershell
# Exportar lista de software para compliance
.\Export-TacticalInventory-Advanced.ps1 -IncludeSoftware -OutputPath "C:\Software\"
```

## 🔍 Solução de Problemas

### Erro de Token
```
Erro ao buscar agentes: Unauthorized
```
**Solução:** Verificar se o token está correto e tem permissões adequadas.

### Módulo ImportExcel não encontrado
```
Módulo ImportExcel não encontrado. Mudando para formato CSV.
```
**Solução:** Instalar o módulo: `Install-Module ImportExcel -Scope CurrentUser`

### Timeout na API
```
Erro ao buscar agentes: The operation has timed out
```
**Solução:** Verificar conectividade com a API ou aumentar o timeout nos scripts.

### Muitos agentes para processar
Para grandes quantidades de agentes, use filtros:
```powershell
# Processar por cliente
.\Export-TacticalInventory-Advanced.ps1 -ClientId 8 -IncludeHardware

# Ou processar apenas agentes online
.\Export-TacticalInventory.ps1 -IncludeOffline $false
```

## 📝 Logs e Debugging

Ambos os scripts incluem logging detalhado. Use `-VerboseOutput` para informações adicionais:

```powershell
.\Export-TacticalInventory.ps1 -VerboseOutput
```

## 🔒 Segurança

- **Nunca** inclua tokens de API diretamente no código em produção
- Use variáveis de ambiente: `$env:TACTICAL_RMM_TOKEN`
- Mantenha os arquivos de exportação em locais seguros
- Revise periodicamente as permissões dos tokens de API

## 📅 Automação

Para automatizar as exportações, crie tarefas agendadas:

```powershell
# Exemplo de tarefa semanal
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\Scripts\Export-TacticalInventory.ps1"
$trigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Monday -At 6AM
Register-ScheduledTask -TaskName "Tactical Inventory Export" -Action $action -Trigger $trigger
```

## 🆘 Suporte

Para problemas ou melhorias, entre em contato com a equipe da NVirtual.
