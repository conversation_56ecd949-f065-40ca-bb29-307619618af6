#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Script para Ubuntu Server - Gera inventário por cliente e envia por email com anexo Excel

.DESCRIPTION
    Este script é otimizado para execução no Ubuntu Server com PowerShell Core.
    Gera inventário do Tactical RMM, cria arquivo Excel e envia por email com anexo.

.PARAMETER ClientId
    ID do cliente no Tactical RMM (obrigatório)

.PARAMETER EmailTo
    Email de destino para envio do relatório (obrigatório)

.PARAMETER EmailSubject
    Assunto do email (opcional)

.PARAMETER IncludeOffline
    Incluir estações offline no relatório (padrão: true)

.PARAMETER OutputPath
    Caminho para salvar arquivos temporários (padrão: /tmp)

.EXAMPLE
    # Execução no Ubuntu Server
    pwsh ./Export-TacticalInventory-Linux.ps1 -ClientId 8 -EmailTo "<EMAIL>"
    
.NOTES
    Versão: 1.0
    Autor: NVirtual
    Data: 2025-01-03
    
    Requisitos Ubuntu:
    - PowerShell Core (pwsh)
    - Módulo ImportExcel: Install-Module ImportExcel -Force
    - Conectividade SMTP
#>

param(
    [Parameter(Mandatory=$true)]
    [int]$ClientId,
    
    [Parameter(Mandatory=$true)]
    [string]$EmailTo,
    
    [string]$EmailSubject = "",
    [bool]$IncludeOffline = $true,
    [string]$OutputPath = "/tmp",
    [string]$SMTPServer = "smtp.gmail.com",
    [int]$SMTPPort = 587,
    [string]$SMTPUser = "<EMAIL>",
    [string]$SMTPPassword = "sua_senha_aqui",
    [switch]$VerboseOutput
)

# ============================================================================
# CONFIGURAÇÕES E INICIALIZAÇÃO
# ============================================================================

# Configurações da API Tactical RMM
$apiUrl = "https://api.centralmesh.nvirtual.com.br"
$apiToken = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"

# Verificar se está rodando no Linux
if (-not $IsLinux) {
    Write-Error "Este script foi projetado para execução no Linux com PowerShell Core"
    exit 1
}

# Verificar e instalar módulo ImportExcel se necessário
try {
    Import-Module ImportExcel -ErrorAction Stop
    Write-Host "✅ Módulo ImportExcel carregado" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Instalando módulo ImportExcel..." -ForegroundColor Yellow
    try {
        Install-Module ImportExcel -Force -Scope CurrentUser -AllowClobber
        Import-Module ImportExcel -Force
        Write-Host "✅ Módulo ImportExcel instalado e carregado" -ForegroundColor Green
    } catch {
        Write-Error "❌ Erro ao instalar módulo ImportExcel: $($_.Exception.Message)"
        exit 1
    }
}

# Criar diretório de saída se não existir
if (-not (Test-Path $OutputPath)) {
    New-Item -Path $OutputPath -ItemType Directory -Force | Out-Null
    Write-Host "📁 Diretório criado: $OutputPath" -ForegroundColor Cyan
}

# ============================================================================
# FUNÇÕES AUXILIARES
# ============================================================================

function Write-TacticalLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "ERROR" { 
            Write-Host $logMessage -ForegroundColor Red
            Write-Error $Message
        }
        "WARN"  { Write-Host $logMessage -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
        default { Write-Host $logMessage -ForegroundColor White }
    }
}

function Get-TacticalData {
    param([string]$Endpoint, [string]$Description)
    
    try {
        Write-TacticalLog "Buscando $Description..."
        
        # Configurar headers para API
        $headers = @{
            "X-API-KEY" = $apiToken
            "Content-Type" = "application/json"
            "User-Agent" = "TacticalInventory-Linux/1.0"
        }
        
        $data = Invoke-RestMethod -Uri "$apiUrl/$Endpoint" -Headers $headers -TimeoutSec 60 -Method Get
        
        Write-TacticalLog "Encontrados $($data.Count) registros de $Description" "SUCCESS"
        return $data
    } catch {
        Write-TacticalLog "Erro ao buscar $Description`: $($_.Exception.Message)" "ERROR"
        throw
    }
}

function Format-InventoryData {
    param($Agent, $Clients, $Sites)
    
    # Buscar informações do cliente e site
    $client = $Clients | Where-Object { $_.id -eq $Agent.client } | Select-Object -First 1
    $site = $Sites | Where-Object { $_.id -eq $Agent.site } | Select-Object -First 1
    
    # Determinar status de conectividade
    $status = if ($Agent.last_seen) {
        $lastSeen = [DateTime]::Parse($Agent.last_seen)
        $timeDiff = (Get-Date) - $lastSeen
        if ($timeDiff.TotalMinutes -lt 5) { "Online" }
        elseif ($timeDiff.TotalHours -lt 24) { "Recente" }
        else { "Offline" }
    } else { "Desconhecido" }
    
    return [PSCustomObject]@{
        'ID_Agente' = $Agent.agent_id
        'Hostname' = $Agent.hostname
        'Cliente' = if ($client) { $client.name } else { "N/A" }
        'Site' = if ($site) { $site.name } else { "N/A" }
        'Status' = $status
        'Sistema_Operacional' = if ($Agent.operating_system) { $Agent.operating_system } else { "N/A" }
        'Versao_OS' = if ($Agent.plat) { $Agent.plat } else { "N/A" }
        'Arquitetura' = if ($Agent.arch) { $Agent.arch } else { "N/A" }
        'IP_Publico' = if ($Agent.public_ip) { $Agent.public_ip } else { "N/A" }
        'Agente_Versao' = if ($Agent.version) { $Agent.version } else { "N/A" }
        'Ultimo_Contato' = if ($Agent.last_seen) { 
            [DateTime]::Parse($Agent.last_seen).ToString("dd/MM/yyyy HH:mm:ss") 
        } else { "N/A" }
        'Tempo_Boot' = if ($Agent.boot_time) { 
            [DateTime]::Parse($Agent.boot_time).ToString("dd/MM/yyyy HH:mm:ss") 
        } else { "N/A" }
        'CPU_Modelo' = if ($Agent.cpu_model) { $Agent.cpu_model } else { "N/A" }
        'RAM_Total_GB' = if ($Agent.total_ram) { 
            [Math]::Round($Agent.total_ram / 1GB, 2) 
        } else { "N/A" }
        'Antivirus' = if ($Agent.antivirus) { $Agent.antivirus } else { "N/A" }
        'Dominio' = if ($Agent.domain) { $Agent.domain } else { "N/A" }
        'Usuario_Logado' = if ($Agent.logged_in_username) { $Agent.logged_in_username } else { "N/A" }
        'Servicos_Falhas' = if ($Agent.services_failing) { $Agent.services_failing } else { 0 }
        'Checks_Falhas' = if ($Agent.checks_failing) { $Agent.checks_failing } else { 0 }
        'Manutencao' = if ($Agent.maintenance_mode) { "Sim" } else { "Não" }
        'Monitoramento' = if ($Agent.monitoring_type) { $Agent.monitoring_type } else { "N/A" }
        'Data_Instalacao' = if ($Agent.install_time) { 
            [DateTime]::Parse($Agent.install_time).ToString("dd/MM/yyyy HH:mm:ss") 
        } else { "N/A" }
        'Observacoes' = if ($Agent.description) { $Agent.description } else { "" }
    }
}

function Export-InventoryToExcel {
    param($InventoryData, $ClientName, $OutputPath)
    
    try {
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $fileName = "TacticalRMM_Inventario_${ClientName}_$timestamp.xlsx"
        $filePath = Join-Path $OutputPath $fileName
        
        Write-TacticalLog "Criando arquivo Excel: $filePath"
        
        # Agrupar dados por site para múltiplas abas
        $siteGroups = $InventoryData | Group-Object Site
        
        # Criar arquivo Excel com múltiplas abas
        foreach ($siteGroup in $siteGroups) {
            $siteName = $siteGroup.Name -replace '[^\w\s-]', '' # Remover caracteres inválidos para nome da aba
            $siteData = $siteGroup.Group
            
            if ($siteGroup -eq $siteGroups[0]) {
                # Primeira aba - criar arquivo
                $siteData | Export-Excel -Path $filePath -WorksheetName $siteName -AutoSize -AutoFilter -FreezeTopRow -BoldTopRow
            } else {
                # Abas adicionais - adicionar ao arquivo existente
                $siteData | Export-Excel -Path $filePath -WorksheetName $siteName -AutoSize -AutoFilter -FreezeTopRow -BoldTopRow -Append
            }
        }
        
        # Adicionar aba de resumo
        $summaryData = @()
        foreach ($siteGroup in $siteGroups) {
            $siteStats = $siteGroup.Group
            $summaryData += [PSCustomObject]@{
                'Site' = $siteGroup.Name
                'Total_Estacoes' = $siteStats.Count
                'Online' = ($siteStats | Where-Object { $_.Status -eq "Online" }).Count
                'Recente' = ($siteStats | Where-Object { $_.Status -eq "Recente" }).Count
                'Offline' = ($siteStats | Where-Object { $_.Status -eq "Offline" }).Count
                'Falhas_Servicos' = ($siteStats | Measure-Object -Property Servicos_Falhas -Sum).Sum
                'Falhas_Checks' = ($siteStats | Measure-Object -Property Checks_Falhas -Sum).Sum
            }
        }
        
        $summaryData | Export-Excel -Path $filePath -WorksheetName "Resumo" -AutoSize -AutoFilter -FreezeTopRow -BoldTopRow -Append
        
        Write-TacticalLog "Arquivo Excel criado com sucesso: $filePath" "SUCCESS"
        return $filePath

    } catch {
        Write-TacticalLog "Erro ao criar arquivo Excel: $($_.Exception.Message)" "ERROR"
        throw
    }
}

function Generate-HTMLReport {
    param($InventoryData, $ClientName, $ReportStats)

    $html = @"
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Relatório de Inventário - $ClientName</title>
    <style>
        body { font-family: 'Segoe UI', Arial, sans-serif; margin: 20px; background-color: #f8f9fa; }
        .header { background: linear-gradient(135deg, #2c3e50, #3498db); color: white; padding: 25px; border-radius: 10px; margin-bottom: 25px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header h1 { margin: 0; font-size: 28px; }
        .header p { margin: 8px 0; opacity: 0.9; font-size: 16px; }
        .stats { display: flex; gap: 20px; margin-bottom: 25px; flex-wrap: wrap; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); min-width: 160px; text-align: center; }
        .stat-number { font-size: 32px; font-weight: bold; color: #2c3e50; margin-bottom: 5px; }
        .stat-label { color: #7f8c8d; font-size: 14px; text-transform: uppercase; letter-spacing: 1px; }
        .site-section { background: white; margin-bottom: 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); overflow: hidden; }
        .site-header { background: linear-gradient(135deg, #34495e, #2c3e50); color: white; padding: 20px; }
        .site-header h2 { margin: 0; font-size: 22px; }
        .agents-table { width: 100%; border-collapse: collapse; }
        .agents-table th, .agents-table td { padding: 15px 12px; text-align: left; border-bottom: 1px solid #ecf0f1; }
        .agents-table th { background-color: #f8f9fa; font-weight: 600; color: #2c3e50; }
        .agents-table tr:hover { background-color: #f8f9fa; }
        .status-online { color: #27ae60; font-weight: bold; }
        .status-recent { color: #f39c12; font-weight: bold; }
        .status-offline { color: #e74c3c; font-weight: bold; }
        .footer { text-align: center; margin-top: 40px; padding: 20px; color: #7f8c8d; font-size: 14px; background: white; border-radius: 10px; }
        .no-agents { padding: 40px; text-align: center; color: #7f8c8d; font-style: italic; }
        .attachment-note { background: #e8f4fd; border: 1px solid #bee5eb; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .attachment-note h3 { margin: 0 0 10px 0; color: #0c5460; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Relatório de Inventário Tactical RMM</h1>
        <p><strong>Cliente:</strong> $ClientName</p>
        <p><strong>Data/Hora:</strong> $(Get-Date -Format "dd/MM/yyyy HH:mm:ss")</p>
        <p><strong>Servidor:</strong> Ubuntu Server - PowerShell Core</p>
        <p><strong>Gerado por:</strong> NVirtual Tactical RMM</p>
    </div>

    <div class="attachment-note">
        <h3>📎 Arquivo Excel em Anexo</h3>
        <p>Este email inclui um arquivo Excel com todos os dados detalhados do inventário, organizados por site em abas separadas.</p>
    </div>

    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">$($ReportStats.TotalAgents)</div>
            <div class="stat-label">Total de Estações</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #27ae60;">$($ReportStats.OnlineCount)</div>
            <div class="stat-label">Online</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #f39c12;">$($ReportStats.RecentCount)</div>
            <div class="stat-label">Recente</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #e74c3c;">$($ReportStats.OfflineCount)</div>
            <div class="stat-label">Offline</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">$($ReportStats.SitesCount)</div>
            <div class="stat-label">Sites</div>
        </div>
    </div>
"@

    # Agrupar por site
    $siteGroups = $InventoryData | Group-Object Site | Sort-Object Name

    foreach ($siteGroup in $siteGroups) {
        $siteName = $siteGroup.Name
        $siteAgents = $siteGroup.Group | Sort-Object Hostname

        $html += @"
    <div class="site-section">
        <div class="site-header">
            <h2>🏢 $siteName ($($siteAgents.Count) estações)</h2>
        </div>
"@

        if ($siteAgents.Count -gt 0) {
            $html += @"
        <table class="agents-table">
            <thead>
                <tr>
                    <th>Hostname</th>
                    <th>Status</th>
                    <th>Sistema Operacional</th>
                    <th>CPU</th>
                    <th>RAM (GB)</th>
                    <th>Último Contato</th>
                    <th>Usuário</th>
                </tr>
            </thead>
            <tbody>
"@

            foreach ($agent in $siteAgents) {
                $statusClass = switch ($agent.Status) {
                    "Online" { "status-online" }
                    "Recente" { "status-recent" }
                    "Offline" { "status-offline" }
                    default { "" }
                }

                $html += @"
                <tr>
                    <td><strong>$($agent.Hostname)</strong></td>
                    <td class="$statusClass">$($agent.Status)</td>
                    <td>$($agent.Sistema_Operacional)</td>
                    <td>$($agent.CPU_Modelo)</td>
                    <td>$($agent.RAM_Total_GB)</td>
                    <td>$($agent.Ultimo_Contato)</td>
                    <td>$($agent.Usuario_Logado)</td>
                </tr>
"@
            }

            $html += @"
            </tbody>
        </table>
"@
        } else {
            $html += '<div class="no-agents">Nenhuma estação encontrada neste site</div>'
        }

        $html += '</div>'
    }

    $html += @"
    <div class="footer">
        <p><strong>Relatório gerado automaticamente pelo sistema Tactical RMM da NVirtual</strong></p>
        <p>Servidor Ubuntu com PowerShell Core | $(Get-Date -Format "dd/MM/yyyy HH:mm:ss")</p>
        <p>Para mais informações, entre em contato com o suporte técnico</p>
    </div>
</body>
</html>
"@

    return $html
}

function Send-InventoryEmailWithAttachment {
    param($HtmlContent, $ClientName, $EmailTo, $EmailSubject, $ReportStats, $ExcelFilePath)

    try {
        # Definir assunto se não fornecido
        if (-not $EmailSubject) {
            $EmailSubject = "📊 Relatório de Inventário - $ClientName - $(Get-Date -Format 'dd/MM/yyyy')"
        }

        Write-TacticalLog "Enviando email para: $EmailTo"
        Write-TacticalLog "Anexo: $(Split-Path $ExcelFilePath -Leaf)"

        # Verificar se o arquivo Excel existe
        if (-not (Test-Path $ExcelFilePath)) {
            throw "Arquivo Excel não encontrado: $ExcelFilePath"
        }

        # Configurar credenciais SMTP
        $securePassword = ConvertTo-SecureString $SMTPPassword -AsPlainText -Force
        $credential = New-Object System.Management.Automation.PSCredential($SMTPUser, $securePassword)

        # Preparar parâmetros do email
        $emailParams = @{
            To = $EmailTo.Split(';').Trim()
            From = $SMTPUser
            Subject = $EmailSubject
            Body = $HtmlContent
            BodyAsHtml = $true
            SmtpServer = $SMTPServer
            Port = $SMTPPort
            UseSsl = $true
            Credential = $credential
            Attachments = $ExcelFilePath
            Encoding = [System.Text.Encoding]::UTF8
        }

        # Enviar email
        Send-MailMessage @emailParams

        Write-TacticalLog "Email enviado com sucesso!" "SUCCESS"
        Write-TacticalLog "Anexo incluído: $(Split-Path $ExcelFilePath -Leaf)" "SUCCESS"
        return $true

    } catch {
        Write-TacticalLog "Erro ao enviar email: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Remove-TempFiles {
    param([string]$FilePath)

    try {
        if (Test-Path $FilePath) {
            Remove-Item $FilePath -Force
            Write-TacticalLog "Arquivo temporário removido: $(Split-Path $FilePath -Leaf)" "SUCCESS"
        }
    } catch {
        Write-TacticalLog "Aviso: Não foi possível remover arquivo temporário: $($_.Exception.Message)" "WARN"
    }
}

# ============================================================================
# EXECUÇÃO PRINCIPAL
# ============================================================================

Write-TacticalLog "=== INICIANDO GERAÇÃO DE INVENTÁRIO LINUX ===" "SUCCESS"
Write-TacticalLog "Cliente ID: $ClientId"
Write-TacticalLog "Email destino: $EmailTo"
Write-TacticalLog "Sistema: $(uname -a)" "INFO"
Write-TacticalLog "PowerShell: $($PSVersionTable.PSVersion)" "INFO"

try {
    # Buscar dados da API
    $agents = Get-TacticalData -Endpoint "agents/" -Description "agentes"
    $clients = Get-TacticalData -Endpoint "clients/" -Description "clientes"
    $sites = Get-TacticalData -Endpoint "clients/sites/" -Description "sites"

    # Encontrar o cliente específico
    $targetClient = $clients | Where-Object { $_.id -eq $ClientId } | Select-Object -First 1
    if (-not $targetClient) {
        Write-TacticalLog "Cliente com ID $ClientId não encontrado!" "ERROR"
        exit 1
    }

    $clientName = $targetClient.name
    Write-TacticalLog "Cliente encontrado: $clientName" "SUCCESS"

    # Filtrar agentes do cliente
    $clientAgents = $agents | Where-Object { $_.client -eq $ClientId }
    Write-TacticalLog "Encontrados $($clientAgents.Count) agentes para o cliente $clientName"

    # Filtrar agentes offline se necessário
    if (-not $IncludeOffline) {
        $clientAgents = $clientAgents | Where-Object {
            if ($_.last_seen) {
                $lastSeen = [DateTime]::Parse($_.last_seen)
                $timeDiff = (Get-Date) - $lastSeen
                $timeDiff.TotalHours -lt 24
            } else {
                $false
            }
        }
        Write-TacticalLog "Filtrado agentes offline ($($clientAgents.Count) agentes restantes)"
    }

    if ($clientAgents.Count -eq 0) {
        Write-TacticalLog "Nenhum agente encontrado para o cliente especificado" "WARN"
        exit 0
    }

    # Processar dados dos agentes
    Write-TacticalLog "Processando dados de $($clientAgents.Count) agentes..."
    $inventoryData = @()

    foreach ($agent in $clientAgents) {
        $formattedAgent = Format-InventoryData -Agent $agent -Clients $clients -Sites $sites
        $inventoryData += $formattedAgent
    }

    # Calcular estatísticas
    $onlineCount = ($inventoryData | Where-Object { $_.Status -eq "Online" }).Count
    $offlineCount = ($inventoryData | Where-Object { $_.Status -eq "Offline" }).Count
    $recentCount = ($inventoryData | Where-Object { $_.Status -eq "Recente" }).Count
    $sitesCount = ($inventoryData | Group-Object Site).Count

    $reportStats = @{
        TotalAgents = $inventoryData.Count
        OnlineCount = $onlineCount
        OfflineCount = $offlineCount
        RecentCount = $recentCount
        SitesCount = $sitesCount
    }

    Write-TacticalLog "=== ESTATÍSTICAS ===" "SUCCESS"
    Write-TacticalLog "Total: $($reportStats.TotalAgents) | Online: $onlineCount | Recente: $recentCount | Offline: $offlineCount | Sites: $sitesCount"

    # Gerar arquivo Excel
    Write-TacticalLog "Gerando arquivo Excel..."
    $excelFilePath = Export-InventoryToExcel -InventoryData $inventoryData -ClientName $clientName -OutputPath $OutputPath

    # Gerar relatório HTML
    Write-TacticalLog "Gerando relatório HTML..."
    $htmlReport = Generate-HTMLReport -InventoryData $inventoryData -ClientName $clientName -ReportStats $reportStats

    # Enviar email com anexo
    $emailSent = Send-InventoryEmailWithAttachment -HtmlContent $htmlReport -ClientName $clientName -EmailTo $EmailTo -EmailSubject $EmailSubject -ReportStats $reportStats -ExcelFilePath $excelFilePath

    if ($emailSent) {
        Write-TacticalLog "=== RELATÓRIO ENVIADO COM SUCESSO ===" "SUCCESS"
        Write-TacticalLog "Cliente: $clientName"
        Write-TacticalLog "Destinatário: $EmailTo"
        Write-TacticalLog "Total de estações: $($reportStats.TotalAgents)"
        Write-TacticalLog "Arquivo Excel: $(Split-Path $excelFilePath -Leaf)"

        # Remover arquivo temporário após envio bem-sucedido
        Remove-TempFiles -FilePath $excelFilePath
    } else {
        Write-TacticalLog "Falha no envio do email" "ERROR"
        Write-TacticalLog "Arquivo Excel mantido em: $excelFilePath" "INFO"
        exit 1
    }

} catch {
    Write-TacticalLog "Erro durante a execução: $($_.Exception.Message)" "ERROR"
    Write-TacticalLog "Stack trace: $($_.ScriptStackTrace)" "ERROR"
    exit 1
}

Write-TacticalLog "=== EXECUÇÃO FINALIZADA ===" "SUCCESS"
