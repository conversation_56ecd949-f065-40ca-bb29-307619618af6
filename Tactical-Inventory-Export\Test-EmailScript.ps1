<#
.SYNOPSIS
    Script de teste para o Export-TacticalInventory-Email.ps1

.DESCRIPTION
    Este script demonstra como usar o Export-TacticalInventory-Email.ps1
    e permite testar diferentes cenários de execução.

.NOTES
    Versão: 1.0
    Autor: NVirtual
    Data: 2025-01-03
#>

param(
    [string]$TestMode = "basic",
    [int]$ClientId = 8,
    [string]$EmailTo = "<EMAIL>"
)

Write-Host "=== TESTE DO SCRIPT DE INVENTÁRIO POR EMAIL ===" -ForegroundColor Cyan
Write-Host "Modo de teste: $TestMode" -ForegroundColor Yellow
Write-Host "Cliente ID: $ClientId" -ForegroundColor Yellow
Write-Host "Email destino: $EmailTo" -ForegroundColor Yellow
Write-Host ""

# Verificar se o script principal existe
$scriptPath = Join-Path $PSScriptRoot "Export-TacticalInventory-Email.ps1"
if (-not (Test-Path $scriptPath)) {
    Write-Host "❌ Script Export-TacticalInventory-Email.ps1 não encontrado!" -ForegroundColor Red
    Write-Host "Caminho esperado: $scriptPath" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Script encontrado: $scriptPath" -ForegroundColor Green
Write-Host ""

switch ($TestMode.ToLower()) {
    "basic" {
        Write-Host "🧪 TESTE BÁSICO - Inventário simples" -ForegroundColor Cyan
        Write-Host "Executando: .\Export-TacticalInventory-Email.ps1 -ClientId $ClientId -EmailTo `"$EmailTo`"" -ForegroundColor Gray
        Write-Host ""
        
        & $scriptPath -ClientId $ClientId -EmailTo $EmailTo
    }
    
    "custom-subject" {
        Write-Host "🧪 TESTE COM ASSUNTO PERSONALIZADO" -ForegroundColor Cyan
        $customSubject = "📊 Teste de Inventário - $(Get-Date -Format 'dd/MM/yyyy HH:mm')"
        Write-Host "Assunto: $customSubject" -ForegroundColor Yellow
        Write-Host ""
        
        & $scriptPath -ClientId $ClientId -EmailTo $EmailTo -EmailSubject $customSubject
    }
    
    "no-offline" {
        Write-Host "🧪 TESTE SEM ESTAÇÕES OFFLINE" -ForegroundColor Cyan
        Write-Host "Executando sem incluir estações offline..." -ForegroundColor Yellow
        Write-Host ""
        
        & $scriptPath -ClientId $ClientId -EmailTo $EmailTo -IncludeOffline $false -EmailSubject "Inventário - Apenas Estações Online"
    }
    
    "verbose" {
        Write-Host "🧪 TESTE COM SAÍDA DETALHADA" -ForegroundColor Cyan
        Write-Host "Executando com verbose output..." -ForegroundColor Yellow
        Write-Host ""
        
        & $scriptPath -ClientId $ClientId -EmailTo $EmailTo -VerboseOutput -EmailSubject "Inventário Detalhado - Teste"
    }
    
    "multiple-emails" {
        Write-Host "🧪 TESTE COM MÚLTIPLOS DESTINATÁRIOS" -ForegroundColor Cyan
        $multipleEmails = "<EMAIL>;<EMAIL>;<EMAIL>"
        Write-Host "Destinatários: $multipleEmails" -ForegroundColor Yellow
        Write-Host ""
        
        & $scriptPath -ClientId $ClientId -EmailTo $multipleEmails -EmailSubject "Inventário - Múltiplos Destinatários"
    }
    
    "all-clients" {
        Write-Host "🧪 TESTE PARA MÚLTIPLOS CLIENTES" -ForegroundColor Cyan
        Write-Host "Executando para diferentes clientes..." -ForegroundColor Yellow
        Write-Host ""
        
        $testClients = @(1, 8)  # IDs de clientes para teste
        
        foreach ($testClientId in $testClients) {
            Write-Host "--- Processando Cliente ID: $testClientId ---" -ForegroundColor Magenta
            $clientSubject = "Inventário Cliente $testClientId - $(Get-Date -Format 'dd/MM/yyyy')"
            
            try {
                & $scriptPath -ClientId $testClientId -EmailTo $EmailTo -EmailSubject $clientSubject
                Write-Host "✅ Cliente $testClientId processado com sucesso" -ForegroundColor Green
            } catch {
                Write-Host "❌ Erro ao processar cliente $testClientId`: $($_.Exception.Message)" -ForegroundColor Red
            }
            
            Write-Host ""
        }
    }
    
    "dry-run" {
        Write-Host "🧪 TESTE DE VALIDAÇÃO (SEM ENVIO DE EMAIL)" -ForegroundColor Cyan
        Write-Host "Este teste valida o script sem enviar email..." -ForegroundColor Yellow
        Write-Host ""
        
        # Simular execução sem envio real
        Write-Host "⚠️  MODO DRY-RUN: Email não será enviado" -ForegroundColor Yellow
        Write-Host "Para implementar dry-run, modifique o script principal para aceitar parâmetro -DryRun" -ForegroundColor Gray
        
        & $scriptPath -ClientId $ClientId -EmailTo $EmailTo -EmailSubject "TESTE - Dry Run"
    }
    
    "help" {
        Write-Host "🆘 AJUDA - MODOS DE TESTE DISPONÍVEIS" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Modos disponíveis:" -ForegroundColor White
        Write-Host "  basic           - Teste básico com parâmetros mínimos" -ForegroundColor Gray
        Write-Host "  custom-subject  - Teste com assunto personalizado" -ForegroundColor Gray
        Write-Host "  no-offline      - Teste excluindo estações offline" -ForegroundColor Gray
        Write-Host "  verbose         - Teste com saída detalhada" -ForegroundColor Gray
        Write-Host "  multiple-emails - Teste com múltiplos destinatários" -ForegroundColor Gray
        Write-Host "  all-clients     - Teste para múltiplos clientes" -ForegroundColor Gray
        Write-Host "  dry-run         - Validação sem envio de email" -ForegroundColor Gray
        Write-Host "  help            - Esta ajuda" -ForegroundColor Gray
        Write-Host ""
        Write-Host "Exemplos de uso:" -ForegroundColor White
        Write-Host "  .\Test-EmailScript.ps1 -TestMode basic" -ForegroundColor Gray
        Write-Host "  .\Test-EmailScript.ps1 -TestMode custom-subject -ClientId 8 -EmailTo `"<EMAIL>`"" -ForegroundColor Gray
        Write-Host "  .\Test-EmailScript.ps1 -TestMode all-clients" -ForegroundColor Gray
    }
    
    default {
        Write-Host "❌ Modo de teste '$TestMode' não reconhecido!" -ForegroundColor Red
        Write-Host "Use -TestMode help para ver os modos disponíveis" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host ""
Write-Host "=== TESTE FINALIZADO ===" -ForegroundColor Cyan

# Mostrar informações úteis
Write-Host ""
Write-Host "📋 INFORMAÇÕES ÚTEIS:" -ForegroundColor White
Write-Host "• Para usar no Tactical RMM, adicione o script Export-TacticalInventory-Email.ps1" -ForegroundColor Gray
Write-Host "• Argumentos mínimos: -ClientId X -EmailTo `"<EMAIL>`"" -ForegroundColor Gray
Write-Host "• Verifique os logs do Tactical RMM para acompanhar a execução" -ForegroundColor Gray
Write-Host "• Consulte README-Email-Script.md para documentação completa" -ForegroundColor Gray
