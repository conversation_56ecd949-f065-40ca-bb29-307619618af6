# ============================================================================
# CONFIGURAÇÃO PARA SCRIPT DE EMAIL - TACTICAL INVENTORY EXPORT
# ============================================================================
# 
# Este arquivo contém configurações específicas para o script
# Export-TacticalInventory-Email.ps1
# 
# INSTRUÇÕES:
# 1. Copie este arquivo para 'config-email.ps1'
# 2. Edite as configurações conforme sua necessidade
# 3. Referencie este arquivo no script ou use as variáveis diretamente
#
# ============================================================================

# ============================================================================
# CONFIGURAÇÕES DE EMAIL SMTP
# ============================================================================

# Configurações para Gmail/Google Workspace
$Global:GMAIL_CONFIG = @{
    "SMTPServer" = "smtp.gmail.com"
    "SMTPPort" = 587
    "UseSSL" = $true
    "RequiresAuth" = $true
    # Para Gmail, use senha de aplicativo, não a senha normal da conta
    "DefaultUser" = "<EMAIL>"
    "DefaultPassword" = "senha_aplicativo_aqui"
}

# Configurações para Outlook/Office 365
$Global:OUTLOOK_CONFIG = @{
    "SMTPServer" = "smtp.office365.com"
    "SMTPPort" = 587
    "UseSSL" = $true
    "RequiresAuth" = $true
    "DefaultUser" = "<EMAIL>"
    "DefaultPassword" = "senha_aqui"
}

# Configurações para servidor SMTP personalizado
$Global:CUSTOM_SMTP_CONFIG = @{
    "SMTPServer" = "mail.empresa.com"
    "SMTPPort" = 587
    "UseSSL" = $true
    "RequiresAuth" = $true
    "DefaultUser" = "<EMAIL>"
    "DefaultPassword" = "senha_aqui"
}

# Configuração ativa (escolha uma das opções acima)
$Global:ACTIVE_EMAIL_CONFIG = $Global:GMAIL_CONFIG

# ============================================================================
# CONFIGURAÇÕES DE RELATÓRIO
# ============================================================================

# Templates de assunto de email
$Global:EMAIL_SUBJECTS = @{
    "Daily" = "📊 Relatório Diário de Inventário - {ClientName} - {Date}"
    "Weekly" = "📊 Relatório Semanal de Inventário - {ClientName} - {Date}"
    "Monthly" = "📊 Relatório Mensal de Inventário - {ClientName} - {Date}"
    "OnDemand" = "📊 Inventário Sob Demanda - {ClientName} - {Date}"
    "Alert" = "⚠️ Alerta de Inventário - {ClientName} - {Date}"
}

# Configurações de formatação HTML
$Global:HTML_CONFIG = @{
    "PrimaryColor" = "#2c3e50"      # Azul escuro
    "SecondaryColor" = "#34495e"    # Azul médio
    "SuccessColor" = "#27ae60"      # Verde
    "WarningColor" = "#f39c12"      # Laranja
    "DangerColor" = "#e74c3c"       # Vermelho
    "BackgroundColor" = "#f5f5f5"   # Cinza claro
    "FontFamily" = "Arial, sans-serif"
    "LogoUrl" = "https://nvirtual.com.br/logo.png"  # URL do logo (opcional)
}

# ============================================================================
# MAPEAMENTO DE CLIENTES E CONFIGURAÇÕES ESPECÍFICAS
# ============================================================================

# Configurações específicas por cliente
$Global:CLIENT_EMAIL_CONFIG = @{
    # Cliente ID 1 - NVirtual Info
    1 = @{
        "DefaultRecipients" = @("<EMAIL>", "<EMAIL>")
        "CCRecipients" = @("<EMAIL>")
        "SubjectPrefix" = "[NVIRTUAL]"
        "IncludeOffline" = $true
        "CustomMessage" = "Relatório interno da NVirtual"
    }
    
    # Cliente ID 8 - Sumire
    8 = @{
        "DefaultRecipients" = @("<EMAIL>")
        "CCRecipients" = @("<EMAIL>")
        "SubjectPrefix" = "[SUMIRE]"
        "IncludeOffline" = $true
        "CustomMessage" = "Relatório de inventário das estações Sumire"
    }
    
    # Adicione outros clientes conforme necessário
    # ClienteID = @{
    #     "DefaultRecipients" = @("<EMAIL>", "<EMAIL>")
    #     "CCRecipients" = @("<EMAIL>")
    #     "SubjectPrefix" = "[CLIENTE]"
    #     "IncludeOffline" = $true
    #     "CustomMessage" = "Mensagem personalizada"
    # }
}

# ============================================================================
# CONFIGURAÇÕES DE AGENDAMENTO
# ============================================================================

# Configurações para execução automática via Tactical RMM
$Global:SCHEDULE_CONFIG = @{
    # Relatórios diários
    "Daily" = @{
        "Enabled" = $true
        "Time" = "08:00"
        "Clients" = @(1, 8)  # IDs dos clientes para relatório diário
        "IncludeOffline" = $false
        "SubjectTemplate" = $Global:EMAIL_SUBJECTS["Daily"]
    }
    
    # Relatórios semanais
    "Weekly" = @{
        "Enabled" = $true
        "DayOfWeek" = "Monday"
        "Time" = "07:00"
        "Clients" = @(1, 8)  # IDs dos clientes para relatório semanal
        "IncludeOffline" = $true
        "SubjectTemplate" = $Global:EMAIL_SUBJECTS["Weekly"]
    }
    
    # Relatórios mensais
    "Monthly" = @{
        "Enabled" = $true
        "DayOfMonth" = 1
        "Time" = "06:00"
        "Clients" = @(1, 8)  # IDs dos clientes para relatório mensal
        "IncludeOffline" = $true
        "SubjectTemplate" = $Global:EMAIL_SUBJECTS["Monthly"]
    }
}

# ============================================================================
# CONFIGURAÇÕES DE FILTROS E ALERTAS
# ============================================================================

# Configurações de alertas automáticos
$Global:ALERT_CONFIG = @{
    # Alerta quando muitas estações estão offline
    "HighOfflineCount" = @{
        "Enabled" = $true
        "Threshold" = 0.3  # 30% das estações offline
        "Recipients" = @("<EMAIL>")
        "Subject" = "⚠️ ALERTA: Alto número de estações offline"
    }
    
    # Alerta quando nenhuma estação está online
    "NoOnlineStations" = @{
        "Enabled" = $true
        "Recipients" = @("<EMAIL>")
        "Subject" = "🚨 CRÍTICO: Nenhuma estação online detectada"
    }
    
    # Alerta para estações com falhas de serviços
    "ServiceFailures" = @{
        "Enabled" = $true
        "Threshold" = 5  # Mais de 5 falhas de serviços
        "Recipients" = @("<EMAIL>")
        "Subject" = "⚠️ ALERTA: Estações com falhas de serviços"
    }
}

# ============================================================================
# CONFIGURAÇÕES DE PERFORMANCE E LIMITES
# ============================================================================

# Limites para evitar sobrecarga
$Global:PERFORMANCE_CONFIG = @{
    "MaxAgentsPerReport" = 500      # Máximo de agentes por relatório
    "MaxEmailSize" = 25MB           # Tamanho máximo do email
    "EmailTimeout" = 300            # Timeout para envio de email (segundos)
    "APITimeout" = 120              # Timeout para chamadas da API (segundos)
    "RetryAttempts" = 3             # Tentativas de reenvio em caso de falha
    "RetryDelay" = 30               # Delay entre tentativas (segundos)
}

# ============================================================================
# CONFIGURAÇÕES DE SEGURANÇA
# ============================================================================

# Configurações de segurança e privacidade
$Global:SECURITY_CONFIG = @{
    "MaskSensitiveData" = $true     # Mascarar dados sensíveis
    "LogEmailContent" = $false      # Não registrar conteúdo do email nos logs
    "EncryptPasswords" = $true      # Usar senhas criptografadas
    "AllowedDomains" = @(           # Domínios permitidos para envio
        "nvirtual.com.br",
        "sumire.com.br",
        "empresa.com"
    )
}

# ============================================================================
# FUNÇÕES AUXILIARES DE CONFIGURAÇÃO
# ============================================================================

function Get-ClientEmailConfig {
    param([int]$ClientId)
    
    if ($Global:CLIENT_EMAIL_CONFIG.ContainsKey($ClientId)) {
        return $Global:CLIENT_EMAIL_CONFIG[$ClientId]
    } else {
        # Configuração padrão para clientes não mapeados
        return @{
            "DefaultRecipients" = @("<EMAIL>")
            "CCRecipients" = @()
            "SubjectPrefix" = "[TACTICAL]"
            "IncludeOffline" = $true
            "CustomMessage" = "Relatório de inventário gerado automaticamente"
        }
    }
}

function Get-EmailSubject {
    param([string]$Type, [string]$ClientName, [string]$Date)
    
    $template = $Global:EMAIL_SUBJECTS[$Type]
    if (-not $template) {
        $template = $Global:EMAIL_SUBJECTS["OnDemand"]
    }
    
    return $template -replace "{ClientName}", $ClientName -replace "{Date}", $Date
}

function Test-EmailConfiguration {
    <#
    .SYNOPSIS
        Testa a configuração de email
    #>
    
    Write-Host "=== TESTE DE CONFIGURAÇÃO DE EMAIL ===" -ForegroundColor Cyan
    
    # Testar configuração SMTP
    $config = $Global:ACTIVE_EMAIL_CONFIG
    Write-Host "Servidor SMTP: $($config.SMTPServer):$($config.SMTPPort)" -ForegroundColor Yellow
    Write-Host "SSL: $($config.UseSSL)" -ForegroundColor Yellow
    Write-Host "Autenticação: $($config.RequiresAuth)" -ForegroundColor Yellow
    Write-Host "Usuário: $($config.DefaultUser)" -ForegroundColor Yellow
    
    # Testar configurações de clientes
    Write-Host "`nClientes configurados:" -ForegroundColor White
    foreach ($clientId in $Global:CLIENT_EMAIL_CONFIG.Keys) {
        $clientConfig = $Global:CLIENT_EMAIL_CONFIG[$clientId]
        Write-Host "  Cliente $clientId`: $($clientConfig.DefaultRecipients -join ', ')" -ForegroundColor Gray
    }
    
    Write-Host "`n✅ Configuração carregada com sucesso!" -ForegroundColor Green
}

# ============================================================================
# INICIALIZAÇÃO
# ============================================================================

# Executar teste de configuração se solicitado
if ($args -contains "-TestConfig") {
    Test-EmailConfiguration
}

Write-Host "📧 Configurações de email carregadas!" -ForegroundColor Green
