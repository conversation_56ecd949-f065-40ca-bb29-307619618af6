#!/bin/bash

# ============================================================================
# CONFIGURAÇÃO DE CRON JOBS - TACTICAL INVENTORY EXPORT
# ============================================================================
# 
# Este script configura cron jobs para execução automática dos relatórios
# de inventário do Tactical RMM no Ubuntu Server
#
# Autor: NVirtual
# Data: 2025-01-03
# Versão: 1.0
#
# ============================================================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Função para log colorido
log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${CYAN}[$timestamp] [INFO]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[$timestamp] [SUCCESS]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[$timestamp] [WARN]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[$timestamp] [ERROR]${NC} $message"
            ;;
    esac
}

# Configurações padrão
SCRIPT_PATH="/usr/local/bin/tactical-inventory"
LOG_DIR="/var/log/tactical-inventory"
BACKUP_DIR="/opt/tactical-inventory/backups"

# Verificar se script existe
check_script() {
    if [ ! -f "$SCRIPT_PATH" ]; then
        log "ERROR" "Script não encontrado: $SCRIPT_PATH"
        log "INFO" "Execute primeiro: ./Setup-Ubuntu-Server.sh"
        exit 1
    fi
    log "SUCCESS" "Script encontrado: $SCRIPT_PATH"
}

# Criar diretórios necessários
create_directories() {
    log "INFO" "Criando diretórios necessários..."
    
    sudo mkdir -p "$LOG_DIR" "$BACKUP_DIR"
    sudo chown -R $USER:$USER "$LOG_DIR" "$BACKUP_DIR"
    sudo chmod -R 755 "$LOG_DIR" "$BACKUP_DIR"
    
    log "SUCCESS" "Diretórios criados"
}

# Backup do crontab atual
backup_crontab() {
    log "INFO" "Fazendo backup do crontab atual..."
    
    local backup_file="$BACKUP_DIR/crontab-backup-$(date +%Y%m%d-%H%M%S).txt"
    crontab -l > "$backup_file" 2>/dev/null || echo "# Crontab vazio" > "$backup_file"
    
    log "SUCCESS" "Backup salvo em: $backup_file"
}

# Configurar cron jobs básicos
setup_basic_cron() {
    log "INFO" "Configurando cron jobs básicos..."
    
    # Criar arquivo temporário com novos jobs
    local temp_cron=$(mktemp)
    
    # Manter cron jobs existentes (exceto tactical-inventory)
    crontab -l 2>/dev/null | grep -v "tactical-inventory" > "$temp_cron" || true
    
    # Adicionar novos jobs
    cat >> "$temp_cron" << 'EOF'

# ============================================================================
# TACTICAL INVENTORY EXPORT - CRON JOBS
# ============================================================================

# Relatório diário Cliente 1 (NVirtual) - 8h
0 8 * * * /usr/local/bin/tactical-inventory -ClientId 1 -EmailTo "<EMAIL>" -EmailSubject "Relatório Diário NVirtual" >> /var/log/tactical-inventory/client1-daily.log 2>&1

# Relatório diário Cliente 8 (Sumire) - 8h30
30 8 * * * /usr/local/bin/tactical-inventory -ClientId 8 -EmailTo "<EMAIL>" -EmailSubject "Relatório Diário Sumire" >> /var/log/tactical-inventory/client8-daily.log 2>&1

# Relatório semanal completo - Segunda-feira 7h
0 7 * * 1 /usr/local/bin/tactical-inventory -ClientId 8 -EmailTo "<EMAIL>" -EmailSubject "Relatório Semanal Completo" >> /var/log/tactical-inventory/weekly.log 2>&1

# Limpeza de logs antigos - Domingo 2h
0 2 * * 0 find /var/log/tactical-inventory -name "*.log" -mtime +30 -delete

EOF
    
    # Aplicar novo crontab
    crontab "$temp_cron"
    rm "$temp_cron"
    
    log "SUCCESS" "Cron jobs básicos configurados"
}

# Configurar cron jobs avançados
setup_advanced_cron() {
    log "INFO" "Configurando cron jobs avançados..."
    
    local temp_cron=$(mktemp)
    
    # Manter cron jobs existentes (exceto tactical-inventory)
    crontab -l 2>/dev/null | grep -v "tactical-inventory" > "$temp_cron" || true
    
    # Adicionar jobs avançados
    cat >> "$temp_cron" << 'EOF'

# ============================================================================
# TACTICAL INVENTORY EXPORT - CRON JOBS AVANÇADOS
# ============================================================================

# Relatórios diários por cliente
0 8 * * * /usr/local/bin/tactical-inventory -ClientId 1 -EmailTo "<EMAIL>" -IncludeOffline false -EmailSubject "Status Diário - Estações Online" >> /var/log/tactical-inventory/client1-online.log 2>&1
30 8 * * * /usr/local/bin/tactical-inventory -ClientId 8 -EmailTo "<EMAIL>" -IncludeOffline false -EmailSubject "Status Diário - Estações Online" >> /var/log/tactical-inventory/client8-online.log 2>&1

# Relatórios semanais completos
0 7 * * 1 /usr/local/bin/tactical-inventory -ClientId 1 -EmailTo "<EMAIL>;<EMAIL>" -EmailSubject "Relatório Semanal NVirtual" >> /var/log/tactical-inventory/client1-weekly.log 2>&1
15 7 * * 1 /usr/local/bin/tactical-inventory -ClientId 8 -EmailTo "<EMAIL>;<EMAIL>" -EmailSubject "Relatório Semanal Sumire" >> /var/log/tactical-inventory/client8-weekly.log 2>&1

# Relatório mensal - Primeiro dia do mês às 6h
0 6 1 * * /usr/local/bin/tactical-inventory -ClientId 1 -EmailTo "<EMAIL>" -EmailSubject "Relatório Mensal NVirtual - $(date +%B\ %Y)" >> /var/log/tactical-inventory/client1-monthly.log 2>&1
15 6 1 * * /usr/local/bin/tactical-inventory -ClientId 8 -EmailTo "<EMAIL>" -EmailSubject "Relatório Mensal Sumire - $(date +%B\ %Y)" >> /var/log/tactical-inventory/client8-monthly.log 2>&1

# Monitoramento de estações críticas - A cada 4 horas (horário comercial)
0 9,13,17 * * 1-5 /usr/local/bin/tactical-inventory -ClientId 8 -EmailTo "<EMAIL>" -IncludeOffline false -EmailSubject "Monitoramento - Estações Críticas" >> /var/log/tactical-inventory/monitoring.log 2>&1

# Manutenção e limpeza
0 2 * * 0 find /var/log/tactical-inventory -name "*.log" -mtime +30 -delete
30 2 * * 0 find /tmp -name "*tactical*" -mtime +7 -delete
0 3 * * 0 /opt/tactical-inventory/maintenance.sh >> /var/log/tactical-inventory/maintenance.log 2>&1

EOF
    
    # Aplicar novo crontab
    crontab "$temp_cron"
    rm "$temp_cron"
    
    log "SUCCESS" "Cron jobs avançados configurados"
}

# Configurar rotação de logs
setup_logrotate() {
    log "INFO" "Configurando rotação de logs..."
    
    sudo tee /etc/logrotate.d/tactical-inventory > /dev/null << 'EOF'
/var/log/tactical-inventory/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 $USER $USER
    postrotate
        # Opcional: reiniciar serviços se necessário
    endscript
}
EOF
    
    log "SUCCESS" "Rotação de logs configurada"
}

# Criar script de manutenção
create_maintenance_script() {
    log "INFO" "Criando script de manutenção..."
    
    sudo tee /opt/tactical-inventory/maintenance.sh > /dev/null << 'EOF'
#!/bin/bash

# Script de manutenção para Tactical Inventory Export
# Executado semanalmente via cron

LOG_FILE="/var/log/tactical-inventory/maintenance.log"

echo "$(date): Iniciando manutenção semanal" >> "$LOG_FILE"

# Limpeza de arquivos temporários antigos
find /tmp -name "*tactical*" -mtime +7 -delete 2>/dev/null
echo "$(date): Arquivos temporários limpos" >> "$LOG_FILE"

# Verificar espaço em disco
DISK_USAGE=$(df /var/log | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 80 ]; then
    echo "$(date): ALERTA - Uso de disco alto: ${DISK_USAGE}%" >> "$LOG_FILE"
    # Opcional: enviar alerta por email
fi

# Verificar se PowerShell está funcionando
if ! pwsh -c "Write-Output 'OK'" > /dev/null 2>&1; then
    echo "$(date): ERRO - PowerShell não está funcionando" >> "$LOG_FILE"
fi

# Verificar módulos PowerShell
if ! pwsh -c "Import-Module ImportExcel -ErrorAction Stop" > /dev/null 2>&1; then
    echo "$(date): ERRO - Módulo ImportExcel não encontrado" >> "$LOG_FILE"
fi

echo "$(date): Manutenção concluída" >> "$LOG_FILE"
EOF
    
    sudo chmod +x /opt/tactical-inventory/maintenance.sh
    sudo chown $USER:$USER /opt/tactical-inventory/maintenance.sh
    
    log "SUCCESS" "Script de manutenção criado"
}

# Testar configuração
test_cron_setup() {
    log "INFO" "Testando configuração..."
    
    # Verificar se cron está rodando
    if ! systemctl is-active --quiet cron; then
        log "ERROR" "Serviço cron não está rodando"
        log "INFO" "Execute: sudo systemctl start cron"
        return 1
    fi
    
    # Verificar crontab
    local cron_count=$(crontab -l | grep -c "tactical-inventory" || true)
    log "SUCCESS" "Cron jobs configurados: $cron_count"
    
    # Verificar logs
    if [ -d "$LOG_DIR" ]; then
        log "SUCCESS" "Diretório de logs: $LOG_DIR"
    else
        log "ERROR" "Diretório de logs não encontrado"
        return 1
    fi
    
    # Testar execução do script
    log "INFO" "Testando execução do script..."
    if "$SCRIPT_PATH" -ClientId 1 -EmailTo "teste@localhost" -SMTPPassword "teste" > /dev/null 2>&1; then
        log "SUCCESS" "Script executável"
    else
        log "WARN" "Script pode ter problemas (normal se credenciais não estão configuradas)"
    fi
    
    return 0
}

# Mostrar status atual
show_status() {
    log "INFO" "=== STATUS ATUAL ==="
    
    echo -e "\n${CYAN}Cron jobs ativos:${NC}"
    crontab -l | grep "tactical-inventory" || echo "Nenhum job encontrado"
    
    echo -e "\n${CYAN}Próximas execuções:${NC}"
    # Mostrar próximas 5 execuções (requer cronie ou similar)
    if command -v crontab >/dev/null 2>&1; then
        echo "Use 'crontab -l' para ver os horários configurados"
    fi
    
    echo -e "\n${CYAN}Logs recentes:${NC}"
    if [ -d "$LOG_DIR" ]; then
        ls -la "$LOG_DIR"/ | head -10
    else
        echo "Diretório de logs não encontrado"
    fi
    
    echo -e "\n${CYAN}Espaço em disco:${NC}"
    df -h /var/log | tail -1
}

# Menu interativo
show_menu() {
    echo -e "${BLUE}"
    echo "============================================================================"
    echo "           CONFIGURAÇÃO DE CRON JOBS - TACTICAL INVENTORY"
    echo "============================================================================"
    echo -e "${NC}"
    
    echo "Escolha uma opção:"
    echo "1) Configuração básica (relatórios diários e semanais)"
    echo "2) Configuração avançada (múltiplos horários e clientes)"
    echo "3) Apenas rotação de logs"
    echo "4) Criar script de manutenção"
    echo "5) Testar configuração atual"
    echo "6) Mostrar status atual"
    echo "7) Remover todos os cron jobs do tactical-inventory"
    echo "8) Sair"
    echo
    read -p "Digite sua escolha (1-8): " choice
    
    case $choice in
        1)
            backup_crontab
            setup_basic_cron
            setup_logrotate
            test_cron_setup
            ;;
        2)
            backup_crontab
            setup_advanced_cron
            setup_logrotate
            create_maintenance_script
            test_cron_setup
            ;;
        3)
            setup_logrotate
            ;;
        4)
            create_maintenance_script
            ;;
        5)
            test_cron_setup
            ;;
        6)
            show_status
            ;;
        7)
            backup_crontab
            local temp_cron=$(mktemp)
            crontab -l 2>/dev/null | grep -v "tactical-inventory" > "$temp_cron" || true
            crontab "$temp_cron"
            rm "$temp_cron"
            log "SUCCESS" "Cron jobs do tactical-inventory removidos"
            ;;
        8)
            log "INFO" "Saindo..."
            exit 0
            ;;
        *)
            log "ERROR" "Opção inválida"
            show_menu
            ;;
    esac
}

# ============================================================================
# EXECUÇÃO PRINCIPAL
# ============================================================================

main() {
    log "INFO" "Iniciando configuração de cron jobs..."
    
    check_script
    create_directories
    
    if [ $# -eq 0 ]; then
        # Modo interativo
        show_menu
    else
        # Modo não-interativo
        case $1 in
            "basic")
                backup_crontab
                setup_basic_cron
                setup_logrotate
                test_cron_setup
                ;;
            "advanced")
                backup_crontab
                setup_advanced_cron
                setup_logrotate
                create_maintenance_script
                test_cron_setup
                ;;
            "status")
                show_status
                ;;
            *)
                echo "Uso: $0 [basic|advanced|status]"
                exit 1
                ;;
        esac
    fi
    
    log "SUCCESS" "Configuração concluída!"
}

# Executar função principal
main "$@"
