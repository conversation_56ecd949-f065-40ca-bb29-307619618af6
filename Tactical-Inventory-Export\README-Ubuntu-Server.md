# 🐧 Export-TacticalInventory-Linux.ps1 - Ubuntu Server

Guia completo para execução do script de inventário Tactical RMM no **Ubuntu Server** com **PowerShell Core** e envio de relatórios por email com **anexo Excel**.

## 🎯 Características Específicas do Linux

### ✅ Vantagens da Versão Linux
- **Performance superior** em servidor dedicado
- **Menor consumo de recursos** comparado ao Windows
- **Execução em background** sem interface gráfica
- **Integração nativa** com cron jobs
- **Logs centralizados** no sistema Linux
- **Maior estabilidade** para execução contínua

### 📋 Diferenças da Versão Windows
- Usa **PowerShell Core** (pwsh) ao invés do Windows PowerShell
- **Anexo Excel** incluído automaticamente no email
- **Paths Linux** (/tmp, /opt, /var/log)
- **Detecção automática** do ambiente Linux
- **Logs coloridos** no terminal
- **Limpeza automática** de arquivos temporários

## 🚀 Instalação Rápida

### 1. Executar Setup Automático
```bash
# Tornar executável
chmod +x Setup-Ubuntu-Server.sh

# Executar instalação completa
./Setup-Ubuntu-Server.sh
```

### 2. Instalação Manual

#### Instalar PowerShell Core
```bash
# Ubuntu 20.04/22.04
wget -q https://packages.microsoft.com/config/ubuntu/$(lsb_release -rs)/packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt update
sudo apt install -y powershell

# Verificar instalação
pwsh --version
```

#### Instalar Módulos PowerShell
```bash
# Instalar ImportExcel
pwsh -c "Install-Module ImportExcel -Force -Scope CurrentUser -AllowClobber"

# Verificar módulo
pwsh -c "Get-Module ImportExcel -ListAvailable"
```

## 📁 Estrutura de Diretórios

```
/opt/tactical-inventory/          # Scripts principais
├── Export-TacticalInventory-Linux.ps1
└── config-linux.ps1

/var/log/tactical-inventory/      # Logs do sistema
├── inventory-YYYYMMDD.log
└── errors.log

/tmp/tactical-inventory/          # Arquivos temporários
├── excel-files/
└── temp-reports/

/usr/local/bin/                   # Scripts wrapper
└── tactical-inventory            # Comando global
```

## 🔧 Configuração

### 1. Configurar Credenciais SMTP
Edite o script ou crie arquivo de configuração:

```powershell
# /opt/tactical-inventory/config-linux.ps1
$Global:SMTP_CONFIG = @{
    Server = "smtp.gmail.com"
    Port = 587
    User = "<EMAIL>"
    Password = "sua_senha_app_gmail"
}
```

### 2. Configurar Permissões
```bash
# Dar permissões de execução
chmod +x /opt/tactical-inventory/Export-TacticalInventory-Linux.ps1

# Configurar proprietário
sudo chown -R $USER:$USER /opt/tactical-inventory/
```

## 🎮 Execução

### 1. Execução Direta
```bash
# Comando completo
pwsh /opt/tactical-inventory/Export-TacticalInventory-Linux.ps1 \
  -ClientId 8 \
  -EmailTo "<EMAIL>" \
  -EmailSubject "Relatório Semanal"

# Com múltiplos destinatários
pwsh /opt/tactical-inventory/Export-TacticalInventory-Linux.ps1 \
  -ClientId 8 \
  -EmailTo "<EMAIL>;<EMAIL>" \
  -IncludeOffline $false
```

### 2. Usando Script Wrapper
```bash
# Comando simplificado
tactical-inventory -ClientId 8 -EmailTo "<EMAIL>"

# Com parâmetros avançados
tactical-inventory \
  -ClientId 8 \
  -EmailTo "<EMAIL>" \
  -SMTPServer "smtp.office365.com" \
  -SMTPUser "<EMAIL>" \
  -SMTPPassword "senha123"
```

### 3. Parâmetros Disponíveis

| Parâmetro | Tipo | Obrigatório | Padrão | Descrição |
|-----------|------|-------------|---------|-----------|
| `ClientId` | int | ✅ | - | ID do cliente no Tactical RMM |
| `EmailTo` | string | ✅ | - | Email(s) de destino (separados por ;) |
| `EmailSubject` | string | ❌ | Auto | Assunto do email |
| `IncludeOffline` | bool | ❌ | true | Incluir estações offline |
| `OutputPath` | string | ❌ | /tmp | Diretório para arquivos temporários |
| `SMTPServer` | string | ❌ | smtp.gmail.com | Servidor SMTP |
| `SMTPPort` | int | ❌ | 587 | Porta SMTP |
| `SMTPUser` | string | ❌ | <EMAIL> | Usuário SMTP |
| `SMTPPassword` | string | ❌ | - | Senha SMTP |

## 📅 Automação com Cron

### 1. Relatório Diário (8h da manhã)
```bash
# Editar crontab
crontab -e

# Adicionar linha
0 8 * * * /usr/local/bin/tactical-inventory -ClientId 8 -EmailTo "<EMAIL>" >> /var/log/tactical-inventory/daily.log 2>&1
```

### 2. Relatório Semanal (Segunda-feira 7h)
```bash
# Relatório semanal completo
0 7 * * 1 /usr/local/bin/tactical-inventory -ClientId 8 -EmailTo "<EMAIL>" -EmailSubject "Relatório Semanal" >> /var/log/tactical-inventory/weekly.log 2>&1
```

### 3. Múltiplos Clientes
```bash
# Cliente 1 - NVirtual (8h)
0 8 * * * /usr/local/bin/tactical-inventory -ClientId 1 -EmailTo "<EMAIL>" >> /var/log/tactical-inventory/client1.log 2>&1

# Cliente 8 - Sumire (8h30)
30 8 * * * /usr/local/bin/tactical-inventory -ClientId 8 -EmailTo "<EMAIL>" >> /var/log/tactical-inventory/client8.log 2>&1
```

## 📊 Conteúdo do Relatório

### 📧 Email HTML
- **Cabeçalho** com informações do cliente e servidor
- **Estatísticas visuais** (cards coloridos)
- **Tabelas por site** com estações organizadas
- **Status colorido** (🟢 Online, 🟡 Recente, 🔴 Offline)
- **Nota sobre anexo Excel**

### 📎 Anexo Excel
- **Múltiplas abas** (uma por site)
- **Aba de resumo** com estatísticas
- **Formatação automática** (AutoSize, AutoFilter)
- **Dados completos** de cada estação
- **Campos detalhados**: CPU, RAM, SO, usuário, etc.

## 🔍 Monitoramento e Logs

### 1. Logs do Sistema
```bash
# Ver logs em tempo real
tail -f /var/log/tactical-inventory/inventory-$(date +%Y%m%d).log

# Logs de erro
tail -f /var/log/tactical-inventory/errors.log

# Logs do cron
tail -f /var/log/cron
```

### 2. Verificar Execução
```bash
# Status do último cron job
grep "tactical-inventory" /var/log/syslog | tail -5

# Verificar arquivos temporários
ls -la /tmp/tactical-inventory/

# Verificar processos
ps aux | grep pwsh
```

### 3. Logs Típicos
```
[2025-01-03 08:00:15] [INFO] === INICIANDO GERAÇÃO DE INVENTÁRIO LINUX ===
[2025-01-03 08:00:15] [INFO] Cliente ID: 8
[2025-01-03 08:00:15] [INFO] Sistema: Linux ubuntu-server 5.4.0-74-generic
[2025-01-03 08:00:16] [SUCCESS] Encontrados 45 registros de agentes
[2025-01-03 08:00:17] [SUCCESS] Cliente encontrado: Sumire
[2025-01-03 08:00:18] [SUCCESS] Arquivo Excel criado com sucesso
[2025-01-03 08:00:20] [SUCCESS] Email enviado com sucesso!
[2025-01-03 08:00:20] [SUCCESS] Anexo incluído: TacticalRMM_Inventario_Sumire_20250103_080020.xlsx
[2025-01-03 08:00:20] [SUCCESS] === EXECUÇÃO FINALIZADA ===
```

## 🧪 Testes

### 1. Teste Completo
```bash
# Executar todos os testes
pwsh ./Test-Linux-Script.ps1 -TestMode full

# Teste específico
pwsh ./Test-Linux-Script.ps1 -TestMode environment
pwsh ./Test-Linux-Script.ps1 -TestMode api -ClientId 8
```

### 2. Teste Manual
```bash
# Teste sem envio de email
pwsh ./Export-TacticalInventory-Linux.ps1 \
  -ClientId 8 \
  -EmailTo "teste@localhost" \
  -SMTPPassword "teste123"
```

## ⚠️ Solução de Problemas

### Erro: "PowerShell não encontrado"
```bash
# Verificar instalação
which pwsh

# Reinstalar se necessário
sudo apt install -y powershell
```

### Erro: "Módulo ImportExcel não encontrado"
```bash
# Instalar módulo
pwsh -c "Install-Module ImportExcel -Force -Scope CurrentUser"

# Verificar instalação
pwsh -c "Get-Module ImportExcel -ListAvailable"
```

### Erro: "Permissão negada"
```bash
# Corrigir permissões
chmod +x /opt/tactical-inventory/Export-TacticalInventory-Linux.ps1
sudo chown -R $USER:$USER /opt/tactical-inventory/
```

### Erro: "Falha no envio de email"
```bash
# Testar conectividade SMTP
telnet smtp.gmail.com 587

# Verificar credenciais
# Para Gmail: usar senha de aplicativo
# Para Office 365: verificar autenticação moderna
```

### Erro: "Cliente não encontrado"
```bash
# Verificar API
curl -H "X-API-KEY: SEU_TOKEN" https://api.centralmesh.nvirtual.com.br/clients/

# Verificar ClientId
pwsh -c "Invoke-RestMethod -Uri 'https://api.centralmesh.nvirtual.com.br/clients/' -Headers @{'X-API-KEY'='SEU_TOKEN'} | Select-Object id,name"
```

## 🔒 Segurança

### 1. Proteger Credenciais
```bash
# Usar variáveis de ambiente
export TACTICAL_SMTP_PASSWORD="sua_senha"

# Ou arquivo de configuração protegido
chmod 600 /opt/tactical-inventory/config-linux.ps1
```

### 2. Logs Seguros
```bash
# Configurar rotação de logs
sudo nano /etc/logrotate.d/tactical-inventory

# Conteúdo:
/var/log/tactical-inventory/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
}
```

## 📈 Performance

### Otimizações Recomendadas
- **Filtrar clientes específicos** para reduzir carga da API
- **Executar em horários de baixo uso**
- **Usar SSD** para arquivos temporários
- **Configurar timeout adequado** para redes lentas
- **Monitorar uso de memória** em servidores pequenos

---

**Desenvolvido por NVirtual** 🚀  
*Versão Linux 1.0 - Janeiro 2025*
