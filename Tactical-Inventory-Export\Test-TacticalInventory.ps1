<#
.SYNOPSIS
    Script de teste e demonstração para os scripts de exportação de inventário do Tactical RMM

.DESCRIPTION
    Este script testa a conectividade com a API do Tactical RMM e demonstra
    as funcionalidades dos scripts de exportação de inventário.

.PARAMETER ApiToken
    Token de API do Tactical RMM

.PARAMETER TestMode
    Modo de teste: 'connectivity', 'basic', 'advanced', 'all'

.EXAMPLE
    .\Test-TacticalInventory.ps1 -TestMode connectivity
    Testa apenas a conectividade com a API

.EXAMPLE
    .\Test-TacticalInventory.ps1 -TestMode all
    Executa todos os testes disponíveis

.NOTES
    Versão: 1.0
    Autor: NVirtual
    Data: 2025-01-02
#>

param(
    [string]$ApiToken = $env:TACTICAL_RMM_TOKEN,
    [ValidateSet('connectivity', 'basic', 'advanced', 'all')]
    [string]$TestMode = 'connectivity',
    [switch]$VerboseOutput
)

# Configurações
$apiUrl = "https://api.centralmesh.nvirtual.com.br"

if (-not $ApiToken) {
    $ApiToken = "N4TXS3T3FUUJTXZYSV6AQ5X9TOZPWHE8"
    Write-Warning "Usando token padrão. Recomenda-se definir TACTICAL_RMM_TOKEN como variável de ambiente."
}

function Write-TestLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    
    switch ($Level) {
        "SUCCESS" { Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red }
        "WARN"    { Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "[$timestamp] ℹ️  $Message" -ForegroundColor Cyan }
        default   { Write-Host "[$timestamp] 📝 $Message" }
    }
}

function Test-APIConnectivity {
    Write-TestLog "=== TESTE DE CONECTIVIDADE ===" "INFO"
    
    try {
        Write-TestLog "Testando conectividade com a API..."
        $response = Invoke-RestMethod -Uri "$apiUrl/agents/" -Headers @{
            "X-API-KEY" = $ApiToken
        } -TimeoutSec 10
        
        Write-TestLog "Conectividade OK - $($response.Count) agentes encontrados" "SUCCESS"
        
        # Testar endpoints específicos
        $endpoints = @(
            @{Name="Clientes"; Url="$apiUrl/clients/"},
            @{Name="Sites"; Url="$apiUrl/clients/sites/"},
            @{Name="Custom Fields"; Url="$apiUrl/core/customfields/"}
        )
        
        foreach ($endpoint in $endpoints) {
            try {
                $result = Invoke-RestMethod -Uri $endpoint.Url -Headers @{
                    "X-API-KEY" = $ApiToken
                } -TimeoutSec 10
                Write-TestLog "$($endpoint.Name): OK ($($result.Count) registros)" "SUCCESS"
            } catch {
                Write-TestLog "$($endpoint.Name): ERRO - $($_.Exception.Message)" "ERROR"
            }
        }
        
        return $true
    } catch {
        Write-TestLog "Falha na conectividade: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-BasicExport {
    Write-TestLog "=== TESTE DE EXPORTAÇÃO BÁSICA ===" "INFO"
    
    if (-not (Test-Path "Export-TacticalInventory.ps1")) {
        Write-TestLog "Script Export-TacticalInventory.ps1 não encontrado" "ERROR"
        return $false
    }
    
    try {
        Write-TestLog "Executando exportação básica de teste..."
        
        # Criar pasta temporária para teste
        $testPath = Join-Path $env:TEMP "TacticalInventoryTest"
        if (-not (Test-Path $testPath)) {
            New-Item -Path $testPath -ItemType Directory | Out-Null
        }
        
        # Executar exportação básica limitada
        $result = & ".\Export-TacticalInventory.ps1" -OutputPath $testPath -OutputFormat CSV
        
        # Verificar se arquivo foi criado
        $csvFiles = Get-ChildItem -Path $testPath -Filter "*.csv" | Sort-Object LastWriteTime -Descending
        
        if ($csvFiles.Count -gt 0) {
            $latestFile = $csvFiles[0]
            $content = Import-Csv $latestFile.FullName
            Write-TestLog "Exportação básica OK - $($content.Count) registros exportados" "SUCCESS"
            Write-TestLog "Arquivo: $($latestFile.Name)" "INFO"
            
            # Mostrar amostra dos dados
            if ($content.Count -gt 0) {
                Write-TestLog "Amostra dos dados exportados:" "INFO"
                $content | Select-Object -First 3 | Format-Table Hostname, Cliente, Site, Status -AutoSize
            }
            
            return $true
        } else {
            Write-TestLog "Nenhum arquivo CSV foi gerado" "ERROR"
            return $false
        }
        
    } catch {
        Write-TestLog "Erro na exportação básica: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-AdvancedExport {
    Write-TestLog "=== TESTE DE EXPORTAÇÃO AVANÇADA ===" "INFO"
    
    if (-not (Test-Path "Export-TacticalInventory-Advanced.ps1")) {
        Write-TestLog "Script Export-TacticalInventory-Advanced.ps1 não encontrado" "ERROR"
        return $false
    }
    
    # Verificar módulo ImportExcel
    try {
        Import-Module ImportExcel -ErrorAction Stop
        Write-TestLog "Módulo ImportExcel disponível" "SUCCESS"
    } catch {
        Write-TestLog "Módulo ImportExcel não encontrado - instale com: Install-Module ImportExcel" "WARN"
        return $false
    }
    
    try {
        Write-TestLog "Executando exportação avançada de teste..."
        
        # Criar pasta temporária para teste
        $testPath = Join-Path $env:TEMP "TacticalInventoryAdvancedTest"
        if (-not (Test-Path $testPath)) {
            New-Item -Path $testPath -ItemType Directory | Out-Null
        }
        
        # Buscar um agente para teste limitado
        $agents = Invoke-RestMethod -Uri "$apiUrl/agents/" -Headers @{
            "X-API-KEY" = $ApiToken
        } -TimeoutSec 30
        
        if ($agents.Count -eq 0) {
            Write-TestLog "Nenhum agente disponível para teste" "WARN"
            return $false
        }
        
        # Pegar o primeiro cliente para teste limitado
        $firstAgent = $agents[0]
        $clientId = $firstAgent.client
        
        Write-TestLog "Testando com cliente ID: $clientId (agente: $($firstAgent.hostname))"
        
        # Executar exportação avançada limitada
        $result = & ".\Export-TacticalInventory-Advanced.ps1" -ClientId $clientId -OutputPath $testPath -IncludeHardware
        
        # Verificar se arquivo foi criado
        $xlsxFiles = Get-ChildItem -Path $testPath -Filter "*.xlsx" | Sort-Object LastWriteTime -Descending
        
        if ($xlsxFiles.Count -gt 0) {
            $latestFile = $xlsxFiles[0]
            Write-TestLog "Exportação avançada OK" "SUCCESS"
            Write-TestLog "Arquivo: $($latestFile.Name)" "INFO"
            Write-TestLog "Tamanho: $([Math]::Round($latestFile.Length / 1KB, 2)) KB" "INFO"
            
            return $true
        } else {
            Write-TestLog "Nenhum arquivo Excel foi gerado" "ERROR"
            return $false
        }
        
    } catch {
        Write-TestLog "Erro na exportação avançada: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Show-SystemInfo {
    Write-TestLog "=== INFORMAÇÕES DO SISTEMA ===" "INFO"
    
    Write-TestLog "PowerShell Version: $($PSVersionTable.PSVersion)"
    Write-TestLog "OS: $($PSVersionTable.OS)"
    Write-TestLog "Execution Policy: $(Get-ExecutionPolicy)"
    
    # Verificar módulos disponíveis
    $modules = @("ImportExcel")
    foreach ($module in $modules) {
        $installed = Get-Module $module -ListAvailable
        if ($installed) {
            Write-TestLog "Módulo $module`: Instalado (v$($installed[0].Version))" "SUCCESS"
        } else {
            Write-TestLog "Módulo $module`: Não instalado" "WARN"
        }
    }
    
    # Verificar scripts
    $scripts = @("Export-TacticalInventory.ps1", "Export-TacticalInventory-Advanced.ps1")
    foreach ($script in $scripts) {
        if (Test-Path $script) {
            $size = (Get-Item $script).Length
            Write-TestLog "Script $script`: Disponível ($([Math]::Round($size / 1KB, 2)) KB)" "SUCCESS"
        } else {
            Write-TestLog "Script $script`: Não encontrado" "ERROR"
        }
    }
}

function Show-APIInfo {
    Write-TestLog "=== INFORMAÇÕES DA API ===" "INFO"
    
    try {
        # Buscar informações básicas
        $agents = Invoke-RestMethod -Uri "$apiUrl/agents/" -Headers @{
            "X-API-KEY" = $ApiToken
        } -TimeoutSec 30
        
        $clients = Invoke-RestMethod -Uri "$apiUrl/clients/" -Headers @{
            "X-API-KEY" = $ApiToken
        } -TimeoutSec 30
        
        $sites = Invoke-RestMethod -Uri "$apiUrl/clients/sites/" -Headers @{
            "X-API-KEY" = $ApiToken
        } -TimeoutSec 30
        
        Write-TestLog "Total de agentes: $($agents.Count)"
        Write-TestLog "Total de clientes: $($clients.Count)"
        Write-TestLog "Total de sites: $($sites.Count)"
        
        # Estatísticas de status
        $onlineCount = 0
        $offlineCount = 0
        $recentCount = 0
        
        foreach ($agent in $agents) {
            if ($agent.last_seen) {
                $lastSeen = [DateTime]::Parse($agent.last_seen)
                $timeDiff = (Get-Date) - $lastSeen
                if ($timeDiff.TotalMinutes -lt 5) { $onlineCount++ }
                elseif ($timeDiff.TotalHours -lt 24) { $recentCount++ }
                else { $offlineCount++ }
            } else {
                $offlineCount++
            }
        }
        
        Write-TestLog "Agentes Online: $onlineCount"
        Write-TestLog "Agentes Recentes: $recentCount"
        Write-TestLog "Agentes Offline: $offlineCount"
        
        # Top 5 clientes por número de agentes
        $clientStats = $agents | Group-Object client | Sort-Object Count -Descending | Select-Object -First 5
        Write-TestLog "Top 5 clientes por agentes:"
        foreach ($stat in $clientStats) {
            $clientName = ($clients | Where-Object { $_.id -eq $stat.Name }).name
            Write-TestLog "  $clientName`: $($stat.Count) agentes"
        }
        
    } catch {
        Write-TestLog "Erro ao buscar informações da API: $($_.Exception.Message)" "ERROR"
    }
}

# ============================================================================
# EXECUÇÃO PRINCIPAL
# ============================================================================

Write-TestLog "=== INICIANDO TESTES DE INVENTÁRIO TACTICAL RMM ===" "SUCCESS"
Write-TestLog "Modo de teste: $TestMode"
Write-TestLog "API URL: $apiUrl"

Show-SystemInfo

$allTestsPassed = $true

# Executar testes baseado no modo selecionado
switch ($TestMode) {
    'connectivity' {
        $allTestsPassed = Test-APIConnectivity
        if ($allTestsPassed) { Show-APIInfo }
    }
    'basic' {
        if (Test-APIConnectivity) {
            $allTestsPassed = Test-BasicExport
        } else {
            $allTestsPassed = $false
        }
    }
    'advanced' {
        if (Test-APIConnectivity) {
            $allTestsPassed = Test-AdvancedExport
        } else {
            $allTestsPassed = $false
        }
    }
    'all' {
        $connectivityOK = Test-APIConnectivity
        if ($connectivityOK) { Show-APIInfo }
        
        $basicOK = if ($connectivityOK) { Test-BasicExport } else { $false }
        $advancedOK = if ($connectivityOK) { Test-AdvancedExport } else { $false }
        
        $allTestsPassed = $connectivityOK -and $basicOK -and $advancedOK
    }
}

Write-TestLog "=== RESULTADO DOS TESTES ===" "INFO"
if ($allTestsPassed) {
    Write-TestLog "Todos os testes passaram com sucesso!" "SUCCESS"
    Write-TestLog "Os scripts de exportação estão prontos para uso." "SUCCESS"
} else {
    Write-TestLog "Alguns testes falharam. Verifique as mensagens acima." "ERROR"
    Write-TestLog "Consulte o README-Export-Inventory.md para solução de problemas." "INFO"
}

Write-TestLog "=== TESTES FINALIZADOS ===" "INFO"
