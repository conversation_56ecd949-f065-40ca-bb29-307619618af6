#!/bin/bash

# ============================================================================
# SETUP PARA UBUNTU SERVER - TACTICAL INVENTORY EXPORT
# ============================================================================
# 
# Este script configura o ambiente Ubuntu Server para executar o script
# Export-TacticalInventory-Linux.ps1
#
# Autor: NVirtual
# Data: 2025-01-03
# Versão: 1.0
#
# ============================================================================

set -e  # Parar execução em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Função para log colorido
log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${CYAN}[$timestamp] [INFO]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[$timestamp] [SUCCESS]${NC} $message"
            ;;
        "WARN")
            echo -e "${YELLOW}[$timestamp] [WARN]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[$timestamp] [ERROR]${NC} $message"
            ;;
        *)
            echo -e "[$timestamp] $message"
            ;;
    esac
}

# Função para verificar se comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Função para verificar se é root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log "WARN" "Executando como root. Recomenda-se executar como usuário normal."
        read -p "Continuar mesmo assim? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "INFO" "Execução cancelada pelo usuário."
            exit 1
        fi
    fi
}

# Função para detectar distribuição
detect_distro() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        DISTRO=$ID
        VERSION=$VERSION_ID
    else
        log "ERROR" "Não foi possível detectar a distribuição Linux"
        exit 1
    fi
    
    log "INFO" "Distribuição detectada: $DISTRO $VERSION"
}

# Função para atualizar sistema
update_system() {
    log "INFO" "Atualizando sistema..."
    
    case $DISTRO in
        "ubuntu"|"debian")
            sudo apt update && sudo apt upgrade -y
            ;;
        "centos"|"rhel"|"fedora")
            sudo yum update -y || sudo dnf update -y
            ;;
        *)
            log "WARN" "Distribuição não reconhecida. Pule a atualização manual se necessário."
            ;;
    esac
    
    log "SUCCESS" "Sistema atualizado"
}

# Função para instalar PowerShell Core
install_powershell() {
    log "INFO" "Verificando instalação do PowerShell Core..."
    
    if command_exists pwsh; then
        local ps_version=$(pwsh -c '$PSVersionTable.PSVersion.ToString()')
        log "SUCCESS" "PowerShell Core já instalado: v$ps_version"
        return 0
    fi
    
    log "INFO" "Instalando PowerShell Core..."
    
    case $DISTRO in
        "ubuntu")
            # Instalar dependências
            sudo apt install -y wget apt-transport-https software-properties-common
            
            # Baixar e instalar pacote Microsoft
            wget -q "https://packages.microsoft.com/config/ubuntu/$VERSION/packages-microsoft-prod.deb"
            sudo dpkg -i packages-microsoft-prod.deb
            rm packages-microsoft-prod.deb
            
            # Atualizar e instalar PowerShell
            sudo apt update
            sudo apt install -y powershell
            ;;
        "debian")
            # Instalar dependências
            sudo apt install -y wget apt-transport-https software-properties-common
            
            # Baixar e instalar pacote Microsoft
            wget -q "https://packages.microsoft.com/config/debian/$VERSION/packages-microsoft-prod.deb"
            sudo dpkg -i packages-microsoft-prod.deb
            rm packages-microsoft-prod.deb
            
            # Atualizar e instalar PowerShell
            sudo apt update
            sudo apt install -y powershell
            ;;
        "centos"|"rhel")
            # Registrar repositório Microsoft
            curl -sSL https://packages.microsoft.com/config/rhel/8/prod.repo | sudo tee /etc/yum.repos.d/microsoft.repo
            
            # Instalar PowerShell
            sudo yum install -y powershell || sudo dnf install -y powershell
            ;;
        "fedora")
            # Registrar repositório Microsoft
            curl -sSL https://packages.microsoft.com/config/fedora/$VERSION/prod.repo | sudo tee /etc/yum.repos.d/microsoft.repo
            
            # Instalar PowerShell
            sudo dnf install -y powershell
            ;;
        *)
            log "ERROR" "Instalação automática do PowerShell não suportada para $DISTRO"
            log "INFO" "Consulte: https://docs.microsoft.com/en-us/powershell/scripting/install/installing-powershell-core-on-linux"
            exit 1
            ;;
    esac
    
    # Verificar instalação
    if command_exists pwsh; then
        local ps_version=$(pwsh -c '$PSVersionTable.PSVersion.ToString()')
        log "SUCCESS" "PowerShell Core instalado com sucesso: v$ps_version"
    else
        log "ERROR" "Falha na instalação do PowerShell Core"
        exit 1
    fi
}

# Função para instalar módulos PowerShell
install_powershell_modules() {
    log "INFO" "Instalando módulos PowerShell necessários..."
    
    # Instalar ImportExcel
    log "INFO" "Instalando módulo ImportExcel..."
    pwsh -c "Install-Module ImportExcel -Force -Scope CurrentUser -AllowClobber"
    
    # Verificar instalação
    local excel_installed=$(pwsh -c "Get-Module ImportExcel -ListAvailable | Select-Object -First 1 | ForEach-Object { \$_.Version.ToString() }")
    if [ -n "$excel_installed" ]; then
        log "SUCCESS" "Módulo ImportExcel instalado: v$excel_installed"
    else
        log "ERROR" "Falha na instalação do módulo ImportExcel"
        exit 1
    fi
}

# Função para criar estrutura de diretórios
create_directories() {
    log "INFO" "Criando estrutura de diretórios..."
    
    local base_dir="/opt/tactical-inventory"
    local log_dir="/var/log/tactical-inventory"
    local temp_dir="/tmp/tactical-inventory"
    
    # Criar diretórios
    sudo mkdir -p "$base_dir" "$log_dir" "$temp_dir"
    
    # Definir permissões
    sudo chown -R $USER:$USER "$base_dir" "$temp_dir"
    sudo chmod -R 755 "$base_dir" "$temp_dir"
    
    # Log directory precisa de permissões especiais
    sudo chown -R $USER:$USER "$log_dir"
    sudo chmod -R 755 "$log_dir"
    
    log "SUCCESS" "Diretórios criados:"
    log "INFO" "  - Scripts: $base_dir"
    log "INFO" "  - Logs: $log_dir"
    log "INFO" "  - Temporários: $temp_dir"
}

# Função para configurar script
configure_script() {
    log "INFO" "Configurando script principal..."
    
    local script_dir="/opt/tactical-inventory"
    local script_name="Export-TacticalInventory-Linux.ps1"
    
    # Copiar script se existir no diretório atual
    if [ -f "./$script_name" ]; then
        sudo cp "./$script_name" "$script_dir/"
        sudo chmod +x "$script_dir/$script_name"
        log "SUCCESS" "Script copiado para $script_dir/$script_name"
    else
        log "WARN" "Script $script_name não encontrado no diretório atual"
        log "INFO" "Você precisará copiar manualmente o script para $script_dir/"
    fi
    
    # Criar script wrapper
    local wrapper_script="/usr/local/bin/tactical-inventory"
    sudo tee "$wrapper_script" > /dev/null << 'EOF'
#!/bin/bash
# Wrapper script para Tactical Inventory Export
exec pwsh /opt/tactical-inventory/Export-TacticalInventory-Linux.ps1 "$@"
EOF
    
    sudo chmod +x "$wrapper_script"
    log "SUCCESS" "Script wrapper criado: $wrapper_script"
}

# Função para testar instalação
test_installation() {
    log "INFO" "Testando instalação..."
    
    # Testar PowerShell
    if ! command_exists pwsh; then
        log "ERROR" "PowerShell não encontrado"
        return 1
    fi
    
    # Testar módulos
    local modules_test=$(pwsh -c "
        try {
            Import-Module ImportExcel -ErrorAction Stop
            Write-Output 'OK'
        } catch {
            Write-Output 'FAIL'
        }
    ")
    
    if [ "$modules_test" != "OK" ]; then
        log "ERROR" "Módulos PowerShell não carregaram corretamente"
        return 1
    fi
    
    # Testar script wrapper
    if [ -f "/usr/local/bin/tactical-inventory" ]; then
        log "SUCCESS" "Script wrapper disponível"
    else
        log "WARN" "Script wrapper não encontrado"
    fi
    
    log "SUCCESS" "Instalação testada com sucesso!"
    return 0
}

# Função para mostrar informações finais
show_final_info() {
    log "SUCCESS" "=== INSTALAÇÃO CONCLUÍDA ==="
    echo
    log "INFO" "Como usar:"
    echo "  # Execução direta:"
    echo "  pwsh /opt/tactical-inventory/Export-TacticalInventory-Linux.ps1 -ClientId 8 -EmailTo '<EMAIL>'"
    echo
    echo "  # Usando wrapper:"
    echo "  tactical-inventory -ClientId 8 -EmailTo '<EMAIL>'"
    echo
    log "INFO" "Configurações importantes:"
    echo "  - Scripts: /opt/tactical-inventory/"
    echo "  - Logs: /var/log/tactical-inventory/"
    echo "  - Temporários: /tmp/tactical-inventory/"
    echo
    log "INFO" "Próximos passos:"
    echo "  1. Configure as credenciais SMTP no script"
    echo "  2. Teste a execução com um cliente"
    echo "  3. Configure cron job se necessário"
    echo
}

# ============================================================================
# EXECUÇÃO PRINCIPAL
# ============================================================================

main() {
    echo -e "${BLUE}"
    echo "============================================================================"
    echo "           SETUP UBUNTU SERVER - TACTICAL INVENTORY EXPORT"
    echo "============================================================================"
    echo -e "${NC}"
    
    log "INFO" "Iniciando configuração do ambiente..."
    
    # Verificações iniciais
    check_root
    detect_distro
    
    # Instalação e configuração
    update_system
    install_powershell
    install_powershell_modules
    create_directories
    configure_script
    
    # Teste final
    if test_installation; then
        show_final_info
    else
        log "ERROR" "Instalação falhou nos testes finais"
        exit 1
    fi
    
    log "SUCCESS" "Setup concluído com sucesso!"
}

# Executar função principal
main "$@"
