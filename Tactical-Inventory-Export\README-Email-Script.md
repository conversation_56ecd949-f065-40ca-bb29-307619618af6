# 📧 Export-TacticalInventory-Email.ps1

Script otimizado para execução via **Tactical RMM Script Manager** que gera inventário de estações por cliente e envia automaticamente por email.

## 🎯 Objetivo

Este script foi desenvolvido especificamente para ser executado pelo gerenciador de scripts do Tactical RMM, permitindo:
- Gerar inventário de um cliente específico
- Organizar estações por site
- Enviar relatório HTML formatado por email
- Execução automatizada via Tactical RMM

## 📋 Parâmetros Obrigatórios

### ClientId (Obrigatório)
- **Tipo:** Integer
- **Descrição:** ID do cliente no Tactical RMM
- **Exemplo:** `8`

### EmailTo (Obrigatório)
- **Tipo:** String
- **Descrição:** Email de destino para envio do relatório
- **Exemplo:** `"<EMAIL>"`

## 📋 Parâmetros Opcionais

### EmailSubject
- **Tipo:** String
- **Padrão:** Auto-gerado com nome do cliente e data
- **Exemplo:** `"Relatório Mensal de Inventário"`

### IncludeOffline
- **Tipo:** Boolean
- **Padrão:** `$true`
- **Descrição:** Incluir estações offline no relatório

### SMTPServer
- **Tipo:** String
- **Padrão:** `"smtp.gmail.com"`
- **Descrição:** Servidor SMTP para envio

### SMTPPort
- **Tipo:** Integer
- **Padrão:** `587`
- **Descrição:** Porta do servidor SMTP

### SMTPUser
- **Tipo:** String
- **Padrão:** `"<EMAIL>"`
- **Descrição:** Usuário para autenticação SMTP

### SMTPPassword
- **Tipo:** String
- **Padrão:** `"sua_senha_aqui"`
- **Descrição:** Senha para autenticação SMTP

## 🚀 Como Usar no Tactical RMM

### 1. Adicionar Script no Tactical RMM

1. Acesse **Settings → Script Manager**
2. Clique em **Add Script**
3. Configure:
   - **Name:** `Inventário por Email`
   - **Description:** `Gera inventário do cliente e envia por email`
   - **Category:** `Inventory`
   - **Script Type:** `PowerShell`
   - **Shell:** `powershell`

4. Cole o conteúdo do arquivo `Export-TacticalInventory-Email.ps1`

### 2. Configurar Argumentos

No campo **Arguments**, use o formato:
```
-ClientId 8 -EmailTo "<EMAIL>"
```

#### Exemplos de Argumentos:

**Básico:**
```
-ClientId 8 -EmailTo "<EMAIL>"
```

**Com assunto personalizado:**
```
-ClientId 8 -EmailTo "<EMAIL>" -EmailSubject "Relatório Mensal - Janeiro 2025"
```

**Excluindo estações offline:**
```
-ClientId 8 -EmailTo "<EMAIL>" -IncludeOffline $false
```

**Com configurações SMTP personalizadas:**
```
-ClientId 8 -EmailTo "<EMAIL>" -SMTPServer "smtp.office365.com" -SMTPPort 587 -SMTPUser "<EMAIL>" -SMTPPassword "MinhaSenh@123"
```

### 3. Executar Script

#### Execução Manual:
1. Vá para **Agents** → Selecione um agente
2. **Tasks** → **Run Script**
3. Selecione o script **Inventário por Email**
4. Adicione os argumentos necessários
5. Clique em **Run Now**

#### Execução Agendada:
1. **Automation Manager** → **Add Task**
2. Configure:
   - **Task Type:** `Run Script`
   - **Script:** `Inventário por Email`
   - **Arguments:** `-ClientId 8 -EmailTo "<EMAIL>"`
   - **Schedule:** Configure conforme necessário

## 📊 Conteúdo do Relatório

### Cabeçalho
- Nome do cliente
- Data e hora de geração
- Logo/identificação NVirtual

### Estatísticas Resumidas
- Total de estações
- Estações online (🟢)
- Estações recentes (🟡)
- Estações offline (🔴)
- Número de sites

### Detalhes por Site
Para cada site do cliente:
- Nome do site
- Quantidade de estações
- Tabela com detalhes de cada estação:
  - Hostname
  - Status (com cores)
  - Sistema operacional
  - Modelo do CPU
  - RAM total (GB)
  - Último contato
  - Usuário logado

## 🔧 Configurações de Email

### Gmail/Google Workspace
```powershell
-SMTPServer "smtp.gmail.com" -SMTPPort 587 -SMTPUser "<EMAIL>" -SMTPPassword "senha_app"
```

### Outlook/Office 365
```powershell
-SMTPServer "smtp.office365.com" -SMTPPort 587 -SMTPUser "<EMAIL>" -SMTPPassword "sua_senha"
```

### Servidor SMTP Personalizado
```powershell
-SMTPServer "mail.empresa.com" -SMTPPort 587 -SMTPUser "<EMAIL>" -SMTPPassword "senha"
```

## 📝 Logs e Monitoramento

O script gera logs detalhados que aparecem no output do Tactical RMM:
- Conexão com API
- Busca de dados
- Processamento de agentes
- Estatísticas
- Status do envio de email

### Exemplo de Log:
```
[2025-01-03 10:30:15] [INFO] === INICIANDO GERAÇÃO DE INVENTÁRIO PARA EMAIL ===
[2025-01-03 10:30:15] [INFO] Cliente ID: 8
[2025-01-03 10:30:15] [INFO] Email destino: <EMAIL>
[2025-01-03 10:30:16] [SUCCESS] Encontrados 45 registros de agentes
[2025-01-03 10:30:17] [SUCCESS] Cliente encontrado: Sumire
[2025-01-03 10:30:17] [INFO] Encontrados 12 agentes para o cliente Sumire
[2025-01-03 10:30:18] [SUCCESS] Total: 12 | Online: 8 | Recente: 2 | Offline: 2 | Sites: 3
[2025-01-03 10:30:19] [SUCCESS] Email enviado com sucesso!
[2025-01-03 10:30:19] [SUCCESS] === RELATÓRIO ENVIADO COM SUCESSO ===
```

## ⚠️ Considerações de Segurança

1. **Senhas SMTP:** Evite colocar senhas diretamente nos argumentos. Use variáveis de ambiente quando possível.

2. **Permissões:** O script usa token de API com permissões de leitura apenas.

3. **Dados Sensíveis:** IPs públicos e informações de sistema são incluídos no relatório.

## 🔍 Solução de Problemas

### Erro: "Cliente não encontrado"
- Verifique se o ClientId está correto
- Confirme se o cliente existe no Tactical RMM

### Erro: "Falha no envio de email"
- Verifique configurações SMTP
- Confirme credenciais de email
- Teste conectividade com servidor SMTP

### Erro: "Token de API inválido"
- Verifique se o token está correto no script
- Confirme permissões do token na API

## 📅 Casos de Uso Recomendados

### Relatório Semanal Automático
Configure task agendada para executar toda segunda-feira às 8h:
```
-ClientId 8 -EmailTo "<EMAIL>" -EmailSubject "Relatório Semanal de Inventário"
```

### Relatório Mensal Detalhado
Execute no primeiro dia útil do mês:
```
-ClientId 8 -EmailTo "<EMAIL>;<EMAIL>" -EmailSubject "Inventário Mensal - $(Get-Date -Format 'MMMM yyyy')"
```

### Monitoramento de Estações Críticas
Execute diariamente excluindo offline:
```
-ClientId 8 -EmailTo "<EMAIL>" -IncludeOffline $false -EmailSubject "Estações Ativas - Monitoramento Diário"
```

---

**Desenvolvido por NVirtual** 🚀  
*Versão 1.0 - Janeiro 2025*
